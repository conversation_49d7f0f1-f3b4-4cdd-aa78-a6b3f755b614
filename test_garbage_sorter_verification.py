#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
垃圾分拣系统功能验证测试脚本
用于验证修改后的系统功能完整性
"""

import sys
import os
import time
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

class GarbageSorterVerificationTest:
    """垃圾分拣系统验证测试类"""
    
    def __init__(self):
        self.test_results = []
        self.passed_tests = 0
        self.failed_tests = 0
        
    def log_test(self, test_name, status, message=""):
        """记录测试结果"""
        result = {
            'test': test_name,
            'status': status,
            'message': message,
            'timestamp': time.time()
        }
        self.test_results.append(result)
        
        if status == "PASS":
            self.passed_tests += 1
            print(f"✅ {test_name}: {message}")
        else:
            self.failed_tests += 1
            print(f"❌ {test_name}: {message}")
    
    def test_imports(self):
        """测试模块导入"""
        print("\n🔍 测试1: 模块导入验证")
        print("=" * 40)
        
        try:
            from garbage_sorter.gui.gui_client import GarbageSorterGUI
            self.log_test("gui_import", "PASS", "GUI客户端导入成功")
        except Exception as e:
            self.log_test("gui_import", "FAIL", f"GUI客户端导入失败: {e}")
            return False
        
        try:
            from garbage_sorter.detection.rknn_detector import RKNNDetector
            self.log_test("detector_import", "PASS", "RKNN检测器导入成功")
        except Exception as e:
            self.log_test("detector_import", "FAIL", f"RKNN检测器导入失败: {e}")
        
        try:
            from garbage_sorter.hardware.orangepi_client import OrangePiClient
            self.log_test("hardware_import", "PASS", "硬件客户端导入成功")
        except Exception as e:
            self.log_test("hardware_import", "FAIL", f"硬件客户端导入失败: {e}")
        
        return True
    
    def test_roi_consistency(self):
        """测试ROI区域定义一致性"""
        print("\n🔍 测试2: ROI区域定义一致性验证")
        print("=" * 40)
        
        try:
            from garbage_sorter.gui.gui_client import GarbageSorterGUI
            
            # 创建临时实例（不启动GUI）
            import tkinter as tk
            root = tk.Tk()
            root.withdraw()  # 隐藏窗口
            
            gui = GarbageSorterGUI(root)
            
            # 检查ROI区域定义
            if hasattr(gui, 'roi_areas') and gui.roi_areas:
                self.log_test("roi_definition", "PASS", f"ROI区域已定义，包含{len(gui.roi_areas)}个区域")
                
                # 验证区域名称
                expected_names = ['检测区域', '左分拣区', '右分拣区']
                actual_names = [roi['name'] for roi in gui.roi_areas]
                
                if all(name in actual_names for name in expected_names):
                    self.log_test("roi_names", "PASS", "ROI区域名称完整")
                else:
                    missing = [name for name in expected_names if name not in actual_names]
                    self.log_test("roi_names", "FAIL", f"缺少ROI区域: {missing}")
                
                # 验证坐标格式
                for roi in gui.roi_areas:
                    area = roi.get('area')
                    if area and len(area) == 4:
                        self.log_test(f"roi_coords_{roi['name']}", "PASS", f"坐标格式正确: {area}")
                    else:
                        self.log_test(f"roi_coords_{roi['name']}", "FAIL", f"坐标格式错误: {area}")
            else:
                self.log_test("roi_definition", "FAIL", "ROI区域未定义")
            
            root.destroy()
            
        except Exception as e:
            self.log_test("roi_consistency", "FAIL", f"ROI一致性测试失败: {e}")
    
    def test_position_detection_logic(self):
        """测试位置检测逻辑"""
        print("\n🔍 测试3: 位置检测逻辑验证")
        print("=" * 40)
        
        try:
            from garbage_sorter.gui.gui_client import GarbageSorterGUI
            
            # 创建临时实例
            import tkinter as tk
            root = tk.Tk()
            root.withdraw()
            
            gui = GarbageSorterGUI(root)
            
            # 测试位置判断函数
            test_cases = [
                (80, "Left Sort"),    # 左分拣区边界
                (160, "Detect Zone"), # 检测区域
                (320, "Detect Zone"), # 检测区域中心
                (480, "Right Sort"),  # 右分拣区边界
                (560, "Right Sort"),  # 右分拣区内
            ]
            
            for x, expected in test_cases:
                result = gui.get_position_by_coordinates(x)
                if result == expected:
                    self.log_test(f"position_x{x}", "PASS", f"位置{x} -> {result}")
                else:
                    self.log_test(f"position_x{x}", "FAIL", f"位置{x} -> {result}, 期望{expected}")
            
            root.destroy()
            
        except Exception as e:
            self.log_test("position_logic", "FAIL", f"位置检测逻辑测试失败: {e}")
    
    def test_function_existence(self):
        """测试关键函数存在性"""
        print("\n🔍 测试4: 关键函数存在性验证")
        print("=" * 40)
        
        try:
            from garbage_sorter.gui.gui_client import GarbageSorterGUI
            
            required_methods = [
                'start_system', 'stop_system', 'on_drop_detected',
                'handle_detection_results', 'process_detection_frame',
                'get_position_by_coordinates', '_check_object_in_detection_area',
                '_validate_state_consistency', '_reset_to_consistent_state',
                'draw_detection_results_for_roi', 'draw_roi_areas_for_roi',
                '_draw_roi_boundaries', 'auto_sort_garbage'
            ]
            
            missing_methods = []
            for method in required_methods:
                if hasattr(GarbageSorterGUI, method):
                    self.log_test(f"method_{method}", "PASS", f"方法{method}存在")
                else:
                    missing_methods.append(method)
                    self.log_test(f"method_{method}", "FAIL", f"方法{method}缺失")
            
            if not missing_methods:
                self.log_test("all_methods", "PASS", "所有必需方法都存在")
            else:
                self.log_test("all_methods", "FAIL", f"缺少{len(missing_methods)}个方法")
                
        except Exception as e:
            self.log_test("function_existence", "FAIL", f"函数存在性测试失败: {e}")
    
    def test_config_loading(self):
        """测试配置加载"""
        print("\n🔍 测试5: 配置加载验证")
        print("=" * 40)
        
        try:
            # 检查配置文件是否存在
            config_path = project_root / "config.json"
            if config_path.exists():
                self.log_test("config_file", "PASS", "配置文件存在")
                
                # 尝试加载配置
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 检查关键配置项
                required_keys = ['detection', 'video', 'hardware']
                for key in required_keys:
                    if key in config:
                        self.log_test(f"config_{key}", "PASS", f"配置项{key}存在")
                    else:
                        self.log_test(f"config_{key}", "FAIL", f"配置项{key}缺失")
            else:
                self.log_test("config_file", "FAIL", "配置文件不存在")
                
        except Exception as e:
            self.log_test("config_loading", "FAIL", f"配置加载测试失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 垃圾分拣系统功能验证测试")
        print("=" * 50)
        
        # 运行各项测试
        if self.test_imports():
            self.test_roi_consistency()
            self.test_position_detection_logic()
            self.test_function_existence()
            self.test_config_loading()
        
        # 输出测试结果
        print(f"\n📊 测试结果总结")
        print("=" * 30)
        print(f"✅ 通过: {self.passed_tests}")
        print(f"❌ 失败: {self.failed_tests}")
        print(f"📈 成功率: {self.passed_tests/(self.passed_tests+self.failed_tests)*100:.1f}%")
        
        # 保存测试结果
        results_file = project_root / "test_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'summary': {
                    'passed': self.passed_tests,
                    'failed': self.failed_tests,
                    'total': self.passed_tests + self.failed_tests,
                    'success_rate': self.passed_tests/(self.passed_tests+self.failed_tests)*100
                },
                'details': self.test_results
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细结果已保存到: {results_file}")
        
        return self.failed_tests == 0

def main():
    """主函数"""
    tester = GarbageSorterVerificationTest()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 所有测试通过！系统功能验证成功。")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查问题并修复。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
