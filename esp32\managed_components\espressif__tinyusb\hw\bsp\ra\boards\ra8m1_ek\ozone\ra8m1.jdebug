
/*********************************************************************
*
*       OnProjectLoad
*
* Function description
*   Project load routine. Required.
*
**********************************************************************
*/
void OnProjectLoad (void) {
  Project.SetTraceSource ("Trace Pins");
  Project.SetDevice ("R7FA8M1AH");
  Project.SetHostIF ("USB", "");
  Project.SetTargetIF ("SWD");
  Project.SetTIFSpeed ("50 MHz");

  Project.AddSvdFile ("$(InstallDir)/Config/CPU/Cortex-M85F.svd");
  Project.AddSvdFile ("../../../../../../../cmsis-svd-data/data/Renesas/R7FA6M5BH.svd");

  File.Open ("../../../../../../examples/cmake-build-ra8m1_ek/device/cdc_msc/cdc_msc.elf");
}
/*********************************************************************
*
*       BeforeTargetConnect
*
* Function description
*   Event handler routine. Optional.
*
**********************************************************************
*/
void BeforeTargetConnect (void) {
  // Trace pin init is done by J-Link script file as J-Link script files are IDE independent
	//Project.SetJLinkScript("../../../debug.jlinkscript");
  Project.SetJLinkScript ("$(ProjectDir)/Renesas_RA8_TracePins.pex");
}

/*********************************************************************
*
*       AfterTargetConnect
*
* Function description
*   Event handler routine. Optional.
*
**********************************************************************
*/
//void AfterTargetConnect (void) {
//}

/*********************************************************************
*
*       TargetDownload
*
* Function description
*   Replaces the default program download routine. Optional.
*
**********************************************************************
*/
//void TargetDownload (void) {
//}

/*********************************************************************
*
*       BeforeTargetDownload
*
* Function description
*   Event handler routine. Optional.
*
**********************************************************************
*/
//void BeforeTargetDownload (void) {
//}

/*********************************************************************
*
*      AfterTargetDownload
*
* Function description
*   Event handler routine. Optional.
*   The default implementation initializes SP and PC to reset values.
*
**********************************************************************
*/
void AfterTargetDownload (void) {
  _SetupTarget();
}

/*********************************************************************
*
*       BeforeTargetDisconnect
*
* Function description
*   Event handler routine. Optional.
*
**********************************************************************
*/
//void BeforeTargetDisconnect (void) {
//}

/*********************************************************************
*
*       AfterTargetDisconnect
*
* Function description
*   Event handler routine. Optional.
*
**********************************************************************
*/
//void AfterTargetDisconnect (void) {
//}

/*********************************************************************
*
*       AfterTargetHalt
*
* Function description
*   Event handler routine. Optional.
*
**********************************************************************
*/
//void AfterTargetHalt (void) {
//}

/*********************************************************************
*
*       BeforeTargetResume
*
* Function description
*   Event handler routine. Optional.
*
**********************************************************************
*/
//void BeforeTargetResume (void) {
//}

/*********************************************************************
*
*       OnSnapshotLoad
*
* Function description
*   Called upon loading a snapshot. Optional.
*
* Additional information
*   This function is used to restore the target state in cases
*   where values cannot simply be written to the target.
*   Typical use: GPIO clock needs to be enabled, before
*   GPIO is configured.
*
**********************************************************************
*/
//void OnSnapshotLoad (void) {
//}

/*********************************************************************
*
*       OnSnapshotSave
*
* Function description
*   Called upon saving a snapshot. Optional.
*
* Additional information
*   This function is usually used to save values of the target
*   state which can either not be trivially read,
*   or need to be restored in a specific way or order.
*   Typically use: Memory Mapped Registers,
*   such as PLL and GPIO configuration.
*
**********************************************************************
*/
//void OnSnapshotSave (void) {
//}

/*********************************************************************
*
*       OnError
*
* Function description
*   Called when an error occurred. Optional.
*
**********************************************************************
*/
//void OnError (void) {
//}

/*********************************************************************
*
*       AfterProjectLoad
*
* Function description
*   After Project load routine. Optional.
*
**********************************************************************
*/
//void AfterProjectLoad (void) {
//}

/*********************************************************************
*
*       _SetupTarget
*
* Function description
*   Setup the target.
*   Called by AfterTargetReset() and AfterTargetDownload().
*
*   Auto-generated function. May be overridden by Ozone.
*
**********************************************************************
*/
void _SetupTarget(void) {
  unsigned int SP;
  unsigned int PC;
  unsigned int VectorTableAddr;

  VectorTableAddr = Elf.GetBaseAddr();
  //
  // Set up initial stack pointer
  //
  SP = Target.ReadU32(VectorTableAddr);
  if (SP != 0xFFFFFFFF) {
    Target.SetReg("SP", SP);
  }
  //
  // Set up entry point PC
  //
  PC = Elf.GetEntryPointPC();
  if (PC != 0xFFFFFFFF) {
    Target.SetReg("PC", PC);
  } else {
    Util.Error("Project script error: failed to set up entry point PC", 1);
  }
}
