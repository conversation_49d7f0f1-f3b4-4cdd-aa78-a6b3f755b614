#MicroXplorer Configuration settings - do not modify
BOOTPATH.BootPathName=LEGACY
BOOTPATH.IPParameters=BootPathName
BOOTPATH.UserSelectedBootPath=LEGACY
BSP_IP_NAME=NUCLEO-H563ZI
CAD.formats=
CAD.pinconfig=
CAD.provider=
CORTEX_M33_NS.userName=CORTEX_M33
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
MMTAppRegionsCount=0
MMTConfigApplied=false
Mcu.CPN=STM32H563ZIT6
Mcu.ContextProject=TrustZoneDisabled
Mcu.Family=STM32H5
Mcu.IP0=BOOTPATH
Mcu.IP1=CORTEX_M33_NS
Mcu.IP10=NUCLEO-H563ZI
Mcu.IP2=DEBUG
Mcu.IP3=ICACHE
Mcu.IP4=MEMORYMAP
Mcu.IP5=NVIC
Mcu.IP6=PWR
Mcu.IP7=RCC
Mcu.IP8=SYS
Mcu.IP9=USB
Mcu.IPNb=11
Mcu.Name=STM32H563ZITx
Mcu.Package=LQFP144
Mcu.Pin0=PE2
Mcu.Pin1=PE3
Mcu.Pin10=PC1
Mcu.Pin11=PA1
Mcu.Pin12=PA2
Mcu.Pin13=PA4
Mcu.Pin14=PA7
Mcu.Pin15=PC4
Mcu.Pin16=PC5
Mcu.Pin17=PB0
Mcu.Pin18=PB13
Mcu.Pin19=PB14
Mcu.Pin2=PE4
Mcu.Pin20=PB15
Mcu.Pin21=PD8
Mcu.Pin22=PD9
Mcu.Pin23=PG4
Mcu.Pin24=PG7
Mcu.Pin25=PA9
Mcu.Pin26=PA11
Mcu.Pin27=PA12
Mcu.Pin28=PA13(JTMS/SWDIO)
Mcu.Pin29=PA14(JTCK/SWCLK)
Mcu.Pin3=PE5
Mcu.Pin30=PA15(JTDI)
Mcu.Pin31=PG11
Mcu.Pin32=PG13
Mcu.Pin33=PB3(JTDO/TRACESWO)
Mcu.Pin34=PB6
Mcu.Pin35=PB7
Mcu.Pin36=VP_ICACHE_VS_ICACHE
Mcu.Pin37=VP_PWR_VS_SECSignals
Mcu.Pin38=VP_PWR_VS_LPOM
Mcu.Pin39=VP_PWR_VS_DBSignals
Mcu.Pin4=PE6
Mcu.Pin40=VP_SYS_VS_Systick
Mcu.Pin41=VP_BOOTPATH_VS_BOOTPATH
Mcu.Pin42=VP_MEMORYMAP_VS_MEMORYMAP
Mcu.Pin5=PC13
Mcu.Pin6=PC14-OSC32_IN(OSC32_IN)
Mcu.Pin7=PC15-OSC32_OUT(OSC32_OUT)
Mcu.Pin8=PF4
Mcu.Pin9=PH0-OSC_IN(PH0)
Mcu.PinsNb=43
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32H563ZITx
MxCube.Version=6.12.1
MxDb.Version=DB.6.0.121
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.EXTI13_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA1.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PA1.GPIO_Label=RMII_REF_CLK
PA1.GPIO_Mode=GPIO_MODE_AF_PP
PA1.GPIO_PuPd=GPIO_NOPULL
PA1.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PA1.Locked=true
PA1.Signal=ETH_REF_CLK
PA11.GPIOParameters=GPIO_Label
PA11.GPIO_Label=USB_FS_N
PA11.Locked=true
PA11.Mode=Device
PA11.Signal=USB_DM
PA12.GPIOParameters=GPIO_Label
PA12.GPIO_Label=USB_FS_P
PA12.Locked=true
PA12.Mode=Device
PA12.Signal=USB_DP
PA13(JTMS/SWDIO).GPIOParameters=GPIO_Label
PA13(JTMS/SWDIO).GPIO_Label=SWDIO
PA13(JTMS/SWDIO).Locked=true
PA13(JTMS/SWDIO).Mode=Trace_Synchro_4bits_JTAG
PA13(JTMS/SWDIO).Signal=DEBUG_JTMS-SWDIO
PA14(JTCK/SWCLK).GPIOParameters=GPIO_Label
PA14(JTCK/SWCLK).GPIO_Label=SWCLK
PA14(JTCK/SWCLK).Locked=true
PA14(JTCK/SWCLK).Mode=Trace_Synchro_4bits_JTAG
PA14(JTCK/SWCLK).Signal=DEBUG_JTCK-SWCLK
PA15(JTDI).GPIOParameters=GPIO_Label
PA15(JTDI).GPIO_Label=T_JTDI
PA15(JTDI).Locked=true
PA15(JTDI).Mode=Trace_Synchro_4bits_JTAG
PA15(JTDI).Signal=DEBUG_JTDI
PA2.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PA2.GPIO_Label=RMII_MDIO
PA2.GPIO_Mode=GPIO_MODE_AF_PP
PA2.GPIO_PuPd=GPIO_NOPULL
PA2.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PA2.Locked=true
PA2.Signal=ETH_MDIO
PA4.GPIOParameters=GPIO_Label
PA4.GPIO_Label=VBUS_SENSE
PA4.Locked=true
PA4.Signal=ADCx_INP18
PA7.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PA7.GPIO_Label=RMII_CRS_DV
PA7.GPIO_Mode=GPIO_MODE_AF_PP
PA7.GPIO_PuPd=GPIO_NOPULL
PA7.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PA7.Locked=true
PA7.Signal=ETH_CRS_DV
PA9.GPIOParameters=GPIO_Label
PA9.GPIO_Label=UCDP_DBn
PA9.Locked=true
PA9.Signal=UCPD1_DB1
PB0.Locked=true
PB0.Signal=GPIO_Output
PB13.GPIOParameters=GPIO_Label
PB13.GPIO_Label=UCPD_CC1
PB13.Locked=true
PB13.Signal=UCPD1_CC1
PB14.GPIOParameters=GPIO_Label
PB14.GPIO_Label=UCPD_CC2
PB14.Locked=true
PB14.Signal=UCPD1_CC2
PB15.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PB15.GPIO_Label=RMII_TXD1
PB15.GPIO_Mode=GPIO_MODE_AF_PP
PB15.GPIO_PuPd=GPIO_NOPULL
PB15.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PB15.Locked=true
PB15.Signal=ETH_TXD1
PB3(JTDO/TRACESWO).GPIOParameters=GPIO_Label
PB3(JTDO/TRACESWO).GPIO_Label=SWO
PB3(JTDO/TRACESWO).Locked=true
PB3(JTDO/TRACESWO).Mode=Trace_Synchro_4bits_JTAG
PB3(JTDO/TRACESWO).Signal=DEBUG_JTDO-SWO
PB6.GPIOParameters=GPIO_Label
PB6.GPIO_Label=ARD_D1_TX
PB6.Locked=true
PB6.Signal=LPUART1_TX
PB7.GPIOParameters=GPIO_Label
PB7.GPIO_Label=ARD_D0_RX
PB7.Locked=true
PB7.Signal=LPUART1_RX
PC1.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PC1.GPIO_Label=RMII_MDC
PC1.GPIO_Mode=GPIO_MODE_AF_PP
PC1.GPIO_PuPd=GPIO_NOPULL
PC1.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PC1.Locked=true
PC1.Signal=ETH_MDC
PC13.Locked=true
PC13.Signal=GPXTI13
PC14-OSC32_IN(OSC32_IN).Locked=true
PC14-OSC32_IN(OSC32_IN).Mode=LSE-External-Oscillator
PC14-OSC32_IN(OSC32_IN).Signal=RCC_OSC32_IN
PC15-OSC32_OUT(OSC32_OUT).Locked=true
PC15-OSC32_OUT(OSC32_OUT).Mode=LSE-External-Oscillator
PC15-OSC32_OUT(OSC32_OUT).Signal=RCC_OSC32_OUT
PC4.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PC4.GPIO_Label=RMII_RXD0
PC4.GPIO_Mode=GPIO_MODE_AF_PP
PC4.GPIO_PuPd=GPIO_NOPULL
PC4.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PC4.Locked=true
PC4.Signal=ETH_RXD0
PC5.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PC5.GPIO_Label=RMII_RXD1
PC5.GPIO_Mode=GPIO_MODE_AF_PP
PC5.GPIO_PuPd=GPIO_NOPULL
PC5.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PC5.Locked=true
PC5.Signal=ETH_RXD1
PD8.Locked=true
PD8.Signal=USART3_TX
PD9.Locked=true
PD9.Signal=USART3_RX
PE2.GPIOParameters=GPIO_Label
PE2.GPIO_Label=TRACE_CK
PE2.Locked=true
PE2.Mode=Trace_Synchro_4bits_JTAG
PE2.Signal=DEBUG_TRACECLK
PE3.GPIOParameters=GPIO_Label
PE3.GPIO_Label=TRACE_D0
PE3.Locked=true
PE3.Mode=Trace_Synchro_4bits_JTAG
PE3.Signal=DEBUG_TRACED0
PE4.GPIOParameters=GPIO_Label
PE4.GPIO_Label=TRACE_D1
PE4.Locked=true
PE4.Mode=Trace_Synchro_4bits_JTAG
PE4.Signal=DEBUG_TRACED1
PE5.GPIOParameters=GPIO_Label
PE5.GPIO_Label=TRACE_D2
PE5.Locked=true
PE5.Mode=Trace_Synchro_4bits_JTAG
PE5.Signal=DEBUG_TRACED2
PE6.GPIOParameters=GPIO_Label
PE6.GPIO_Label=TRACE_D3
PE6.Locked=true
PE6.Mode=Trace_Synchro_4bits_JTAG
PE6.Signal=DEBUG_TRACED3
PF4.Locked=true
PF4.Signal=GPIO_Output
PG11.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PG11.GPIO_Label=RMII_TXT_EN
PG11.GPIO_Mode=GPIO_MODE_AF_PP
PG11.GPIO_PuPd=GPIO_NOPULL
PG11.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PG11.Locked=true
PG11.Signal=ETH_TX_EN
PG13.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_Mode
PG13.GPIO_Label=RMI_TXD0
PG13.GPIO_Mode=GPIO_MODE_AF_PP
PG13.GPIO_PuPd=GPIO_NOPULL
PG13.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PG13.Locked=true
PG13.Signal=ETH_TXD0
PG4.Locked=true
PG4.Signal=GPIO_Output
PG7.GPIOParameters=GPIO_Label
PG7.GPIO_Label=UCPD_FLT
PG7.Locked=true
PG7.Signal=GPXTI7
PH0-OSC_IN(PH0).GPIOParameters=GPIO_Label
PH0-OSC_IN(PH0).GPIO_Label=STLK_MCO
PH0-OSC_IN(PH0).Locked=true
PH0-OSC_IN(PH0).Mode=HSE-External-Clock-Source
PH0-OSC_IN(PH0).Signal=RCC_OSC_IN
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32H563ZITx
ProjectManager.FirmwarePackage=STM32Cube FW_H5 V1.3.0
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=2
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=stm32h563nucleo.ioc
ProjectManager.ProjectName=stm32h563nucleo
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=Makefile
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_USB_PCD_Init-USB-false-HAL-true,4-MX_ICACHE_Init-ICACHE-false-HAL-true,0-MX_CORTEX_M33_NS_Init-CORTEX_M33_NS-false-HAL-true,0-MX_PWR_Init-PWR-false-HAL-true
RCC.ADCFreq_Value=*********
RCC.AHBFreq_Value=*********
RCC.APB1Freq_Value=*********
RCC.APB1TimFreq_Value=*********
RCC.APB2Freq_Value=*********
RCC.APB2TimFreq_Value=*********
RCC.APB3Freq_Value=*********
RCC.CECFreq_Value=32000
RCC.CKPERFreq_Value=64000000
RCC.CRSFreq_Value=48000000
RCC.CSI_VALUE=4000000
RCC.CortexFreq_Value=*********
RCC.DACFreq_Value=32768
RCC.EPOD_VALUE=8000000
RCC.ETHFreq_Value=*********
RCC.FCLKCortexFreq_Value=*********
RCC.FDCANFreq_Value=8000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=*********
RCC.HSE_VALUE=8000000
RCC.HSI48_VALUE=48000000
RCC.HSIDiv=RCC_HSI_DIV1
RCC.HSI_VALUE=64000000
RCC.I2C1Freq_Value=*********
RCC.I2C2Freq_Value=*********
RCC.I2C3Freq_Value=*********
RCC.I2C4Freq_Value=*********
RCC.I3C1Freq_Value=*********
RCC.IPParameters=ADCFreq_Value,AHBFreq_Value,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,APB3Freq_Value,CECFreq_Value,CKPERFreq_Value,CRSFreq_Value,CSI_VALUE,CortexFreq_Value,DACFreq_Value,EPOD_VALUE,ETHFreq_Value,FCLKCortexFreq_Value,FDCANFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI48_VALUE,HSIDiv,HSI_VALUE,I2C1Freq_Value,I2C2Freq_Value,I2C3Freq_Value,I2C4Freq_Value,I3C1Freq_Value,LPTIM1Freq_Value,LPTIM2Freq_Value,LPTIM3Freq_Value,LPTIM4Freq_Value,LPTIM5Freq_Value,LPTIM6Freq_Value,LPUART1Freq_Value,LSCOPinFreq_Value,LSIRC_VALUE,MCO1PinFreq_Value,MCO2PinFreq_Value,OCTOSPIMFreq_Value,PLL2PoutputFreq_Value,PLL2QoutputFreq_Value,PLL2RoutputFreq_Value,PLL2Source,PLL3FRACN,PLL3N,PLL3PoutputFreq_Value,PLL3Q,PLL3QoutputFreq_Value,PLL3RoutputFreq_Value,PLL3Source,PLLFRACN,PLLM,PLLN,PLLPoutputFreq_Value,PLLQoutputFreq_Value,PLLSourceVirtual,PWRFreq_Value,RNGFreq_Value,SAI1Freq_Value,SAI2Freq_Value,SDMMC1Freq_Value,SDMMC2Freq_Value,SPI1Freq_Value,SPI2Freq_Value,SPI3Freq_Value,SPI4Freq_Value,SPI5Freq_Value,SPI6Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,UART12Freq_Value,UART4Freq_Value,UART5Freq_Value,UART7Freq_Value,UART8Freq_Value,UART9Freq_Value,UCPD1outputFreq_Value,USART10Freq_Value,USART11Freq_Value,USART1Freq_Value,USART2Freq_Value,USART3Freq_Value,USART6Freq_Value,USBCLockSelection,USBFreq_Value,VCOInput2Freq_Value,VCOInput3Freq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VCOPLL2OutputFreq_Value,VCOPLL3OutputFreq_Value
RCC.LPTIM1Freq_Value=*********
RCC.LPTIM2Freq_Value=*********
RCC.LPTIM3Freq_Value=*********
RCC.LPTIM4Freq_Value=*********
RCC.LPTIM5Freq_Value=*********
RCC.LPTIM6Freq_Value=*********
RCC.LPUART1Freq_Value=*********
RCC.LSCOPinFreq_Value=32000
RCC.LSIRC_VALUE=32000
RCC.MCO1PinFreq_Value=64000000
RCC.MCO2PinFreq_Value=*********
RCC.OCTOSPIMFreq_Value=*********
RCC.PLL2PoutputFreq_Value=*********
RCC.PLL2QoutputFreq_Value=*********
RCC.PLL2RoutputFreq_Value=*********
RCC.PLL2Source=RCC_PLL2_SOURCE_HSE
RCC.PLL3FRACN=0
RCC.PLL3N=18
RCC.PLL3PoutputFreq_Value=72000000
RCC.PLL3Q=3
RCC.PLL3QoutputFreq_Value=48000000
RCC.PLL3RoutputFreq_Value=72000000
RCC.PLL3Source=RCC_PLL3_SOURCE_HSE
RCC.PLLFRACN=0
RCC.PLLM=4
RCC.PLLN=250
RCC.PLLPoutputFreq_Value=*********
RCC.PLLQoutputFreq_Value=*********
RCC.PLLSourceVirtual=RCC_PLL1_SOURCE_HSE
RCC.PWRFreq_Value=*********
RCC.RNGFreq_Value=48000000
RCC.SAI1Freq_Value=*********
RCC.SAI2Freq_Value=*********
RCC.SDMMC1Freq_Value=*********
RCC.SDMMC2Freq_Value=*********
RCC.SPI1Freq_Value=*********
RCC.SPI2Freq_Value=*********
RCC.SPI3Freq_Value=*********
RCC.SPI4Freq_Value=*********
RCC.SPI5Freq_Value=*********
RCC.SPI6Freq_Value=*********
RCC.SYSCLKFreq_VALUE=*********
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.UART12Freq_Value=*********
RCC.UART4Freq_Value=*********
RCC.UART5Freq_Value=*********
RCC.UART7Freq_Value=*********
RCC.UART8Freq_Value=*********
RCC.UART9Freq_Value=*********
RCC.UCPD1outputFreq_Value=16000000
RCC.USART10Freq_Value=*********
RCC.USART11Freq_Value=*********
RCC.USART1Freq_Value=*********
RCC.USART2Freq_Value=*********
RCC.USART3Freq_Value=*********
RCC.USART6Freq_Value=*********
RCC.USBCLockSelection=RCC_USBCLKSOURCE_PLL3Q
RCC.USBFreq_Value=48000000
RCC.VCOInput2Freq_Value=8000000
RCC.VCOInput3Freq_Value=8000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=500000000
RCC.VCOPLL2OutputFreq_Value=1032000000
RCC.VCOPLL3OutputFreq_Value=144000000
SH.ADCx_INP18.0=ADC1_INP18
SH.ADCx_INP18.ConfNb=1
SH.GPXTI13.0=GPIO_EXTI13
SH.GPXTI13.ConfNb=1
SH.GPXTI7.0=GPIO_EXTI7
SH.GPXTI7.ConfNb=1
USB.IPParameters=VirtualMode
USB.VirtualMode=Device_Only
VP_BOOTPATH_VS_BOOTPATH.Mode=BP_Activate
VP_BOOTPATH_VS_BOOTPATH.Signal=BOOTPATH_VS_BOOTPATH
VP_ICACHE_VS_ICACHE.Mode=DirectMappedCache
VP_ICACHE_VS_ICACHE.Signal=ICACHE_VS_ICACHE
VP_MEMORYMAP_VS_MEMORYMAP.Mode=CurAppReg
VP_MEMORYMAP_VS_MEMORYMAP.Signal=MEMORYMAP_VS_MEMORYMAP
VP_PWR_VS_DBSignals.Mode=DisableDeadBatterySignals
VP_PWR_VS_DBSignals.Signal=PWR_VS_DBSignals
VP_PWR_VS_LPOM.Mode=PowerOptimisation
VP_PWR_VS_LPOM.Signal=PWR_VS_LPOM
VP_PWR_VS_SECSignals.Mode=Security/Privilege
VP_PWR_VS_SECSignals.Signal=PWR_VS_SECSignals
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
board=NUCLEO-H563ZI
boardIOC=true
