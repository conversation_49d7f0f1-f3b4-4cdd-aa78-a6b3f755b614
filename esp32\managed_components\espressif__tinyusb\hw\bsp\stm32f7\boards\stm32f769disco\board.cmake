set(MCU_VARIANT stm32f769xx)
set(JLINK_DEVICE stm32f769ni)

set(LD_FILE_GNU ${CMAKE_CURRENT_LIST_DIR}/STM32F769ZITx_FLASH.ld)

set(RHPORT_SPEED OPT_MODE_FULL_SPEED OPT_MODE_HIGH_SPEED)

if (NOT DEFINED RHPORT_DEVICE)
  set(RHPORT_DEVICE 1)
endif()
if (NOT DEFINED RHPORT_HOST)
  set(RHPORT_HOST 1)
endif()

function(update_board TARGET)
  target_compile_definitions(${TARGET} PUBLIC
    STM32F769xx
    HSE_VALUE=25000000
    )
endfunction()
