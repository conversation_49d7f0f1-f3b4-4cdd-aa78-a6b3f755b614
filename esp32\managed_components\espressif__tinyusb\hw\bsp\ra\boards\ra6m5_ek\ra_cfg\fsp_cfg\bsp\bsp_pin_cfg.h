/* generated configuration header file - do not edit */
#ifndef BSP_PIN_CFG_H_
#define BSP_PIN_CFG_H_
#include "r_ioport.h"

/* Common macro for FSP header files. There is also a corresponding FSP_FOOTER macro at the end of this file. */
FSP_HEADER

#define MIKROBUS_AN_ARDUINO_A0 (BSP_IO_PORT_00_PIN_00)
#define ARDUINO_A1 (BSP_IO_PORT_00_PIN_01)
#define ARDUINO_A2 (BSP_IO_PORT_00_PIN_02)
#define ARDUINO_A3 (BSP_IO_PORT_00_PIN_03)
#define SW2 (BSP_IO_PORT_00_PIN_04)
#define SW1 (BSP_IO_PORT_00_PIN_05)
#define LED1 (BSP_IO_PORT_00_PIN_06)
#define LED2 (BSP_IO_PORT_00_PIN_07)
#define LED3 (BSP_IO_PORT_00_PIN_08)
#define ARDUINO_A4 (BSP_IO_PORT_00_PIN_14)
#define ARDUINO_A5 (BSP_IO_PORT_00_PIN_15)
#define OSPI_CLK (BSP_IO_PORT_01_PIN_00)
#define OSPI_SIO7 (BSP_IO_PORT_01_PIN_01)
#define OSPI_SIO1 (BSP_IO_PORT_01_PIN_02)
#define OSPI_SIO6 (BSP_IO_PORT_01_PIN_03)
#define OSPI_DQS (BSP_IO_PORT_01_PIN_04)
#define OSPI_SIO5 (BSP_IO_PORT_01_PIN_05)
#define OSPI_SIO0 (BSP_IO_PORT_01_PIN_06)
#define OSPI_SIO3 (BSP_IO_PORT_01_PIN_07)
#define MIKROBUS_PWM_ARDUINO_D3_PWM (BSP_IO_PORT_01_PIN_11)
#define ARDUINO_D4 (BSP_IO_PORT_01_PIN_12)
#define ARDUINO_D5 (BSP_IO_PORT_01_PIN_13)
#define ARDUINO_D6 (BSP_IO_PORT_01_PIN_14)
#define ARDUINO_D9 (BSP_IO_PORT_01_PIN_15)
#define MIKROBUS_MISO_ARDUINO_MISO_PMOD1_MISO (BSP_IO_PORT_02_PIN_02)
#define MIKROBUS_MOSI_ARDUINO_MOSI_PMOD1_MOSI (BSP_IO_PORT_02_PIN_03)
#define MIKROBUS_SCK_ARDUINO_SCK_PMOD1_SCK (BSP_IO_PORT_02_PIN_04)
#define MIKROBUS_SS_ARDUINO_SS (BSP_IO_PORT_02_PIN_05)
#define PMOD1_SS (BSP_IO_PORT_02_PIN_06)
#define ARDUINO_D8 (BSP_IO_PORT_02_PIN_07)
#define PMOD1_SS2 (BSP_IO_PORT_03_PIN_01)
#define PMOD1_SS3 (BSP_IO_PORT_03_PIN_02)
#define MIKROBUS_RESET_ARDUINO_RESET (BSP_IO_PORT_03_PIN_03)
#define QSPI_CLK (BSP_IO_PORT_03_PIN_05)
#define QSPI_CS (BSP_IO_PORT_03_PIN_06)
#define QSPI_IO0 (BSP_IO_PORT_03_PIN_07)
#define QSPI_IO1 (BSP_IO_PORT_03_PIN_08)
#define QSPI_IO2 (BSP_IO_PORT_03_PIN_09)
#define QSPI_IO3 (BSP_IO_PORT_03_PIN_10)
#define PMOD1_RST (BSP_IO_PORT_03_PIN_11)
#define PMOD2_INT (BSP_IO_PORT_04_PIN_00)
#define ETH_MDC (BSP_IO_PORT_04_PIN_01)
#define ETH_MDIO (BSP_IO_PORT_04_PIN_02)
#define ETH_RST (BSP_IO_PORT_04_PIN_03)
#define PMOD2_RST (BSP_IO_PORT_04_PIN_04)
#define ETH_TXEN (BSP_IO_PORT_04_PIN_05)
#define ETH_TXD1 (BSP_IO_PORT_04_PIN_06)
#define USBFS_VBUS (BSP_IO_PORT_04_PIN_07)
#define PMOD2_SS2 (BSP_IO_PORT_04_PIN_08)
#define MIKROBUS_INT_ARDUINO_INT0 (BSP_IO_PORT_04_PIN_09)
#define PMOD2_MISO (BSP_IO_PORT_04_PIN_10)
#define PMOD2_MOSI (BSP_IO_PORT_04_PIN_11)
#define PMOD2_SCK (BSP_IO_PORT_04_PIN_12)
#define PMOS2_SS (BSP_IO_PORT_04_PIN_13)
#define GROVE1_SDA_QWIIC_SDA (BSP_IO_PORT_04_PIN_14)
#define GROVE1_SCL_QWIIC_SCL (BSP_IO_PORT_04_PIN_15)
#define USBFS_VBUS_EN (BSP_IO_PORT_05_PIN_00)
#define USBFS_OVERCURA (BSP_IO_PORT_05_PIN_01)
#define GROVE2_SCL (BSP_IO_PORT_05_PIN_05)
#define GROVE2_SDA (BSP_IO_PORT_05_PIN_06)
#define MIKROBUS_SDA_ARDUINO_SDA (BSP_IO_PORT_05_PIN_11)
#define MIKROBUS_SCL_ARDUINO_SCL (BSP_IO_PORT_05_PIN_12)
#define OSPI_SIO4 (BSP_IO_PORT_06_PIN_00)
#define OSPI_SIO2 (BSP_IO_PORT_06_PIN_01)
#define OSPI_CS1 (BSP_IO_PORT_06_PIN_02)
#define ARDUINO_D7 (BSP_IO_PORT_06_PIN_08)
#define CAN_TXD (BSP_IO_PORT_06_PIN_09)
#define CAN_RDX (BSP_IO_PORT_06_PIN_10)
#define CAN_STBY (BSP_IO_PORT_06_PIN_11)
#define MIKROBUS_TX_ARDUINO_TX (BSP_IO_PORT_06_PIN_13)
#define MIKROBUS_RX_ARDUINO_RX (BSP_IO_PORT_06_PIN_14)
#define OSPI_RST (BSP_IO_PORT_06_PIN_15)
#define ETH_TXD0 (BSP_IO_PORT_07_PIN_00)
#define ETH_50REF (BSP_IO_PORT_07_PIN_01)
#define ETH_RXD0 (BSP_IO_PORT_07_PIN_02)
#define ETH_RXD1 (BSP_IO_PORT_07_PIN_03)
#define ETH_RXERR (BSP_IO_PORT_07_PIN_04)
#define ETH_CRSDV (BSP_IO_PORT_07_PIN_05)
#define ETH_INT (BSP_IO_PORT_07_PIN_06)
#define USBHS_OVERCURA (BSP_IO_PORT_07_PIN_07)
#define PMOD2_SS3 (BSP_IO_PORT_07_PIN_08)
#define PMOD1_INT (BSP_IO_PORT_09_PIN_05)
#define USBHS_VBUS_EN (BSP_IO_PORT_11_PIN_00)
#define USBHS_VBUS (BSP_IO_PORT_11_PIN_01)
extern const ioport_cfg_t g_bsp_pin_cfg; /* RA6M5 EK */

void BSP_PinConfigSecurityInit();

/* Common macro for FSP header files. There is also a corresponding FSP_HEADER macro at the top of this file. */
FSP_FOOTER
#endif /* BSP_PIN_CFG_H_ */
