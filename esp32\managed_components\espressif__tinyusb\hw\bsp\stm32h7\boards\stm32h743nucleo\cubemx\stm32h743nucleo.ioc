#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
ETH.IPParameters=MediaInterface
ETH.MediaInterface=HAL_ETH_RMII_MODE
File.Version=6
KeepUserPlacement=false
Mcu.CPN=STM32H743ZIT6
Mcu.Family=STM32H7
Mcu.IP0=CORTEX_M7
Mcu.IP1=ETH
Mcu.IP2=NVIC
Mcu.IP3=RCC
Mcu.IP4=SYS
Mcu.IP5=USART3
Mcu.IP6=USB_OTG_FS
Mcu.IPNb=7
Mcu.Name=STM32H743ZITx
Mcu.Package=LQFP144
Mcu.Pin0=PC13
Mcu.Pin1=PC14-OSC32_IN (OSC32_IN)
Mcu.Pin10=PC5
Mcu.Pin11=PB0
Mcu.Pin12=PB13
Mcu.Pin13=PB14
Mcu.Pin14=PD8
Mcu.Pin15=PD9
Mcu.Pin16=PD10
Mcu.Pin17=PG7
Mcu.Pin18=PA8
Mcu.Pin19=PA9
Mcu.Pin2=PC15-OSC32_OUT (OSC32_OUT)
Mcu.Pin20=PA11
Mcu.Pin21=PA12
Mcu.Pin22=PG11
Mcu.Pin23=PG13
Mcu.Pin24=PE1
Mcu.Pin25=VP_SYS_VS_Systick
Mcu.Pin3=PH0-OSC_IN (PH0)
Mcu.Pin4=PH1-OSC_OUT (PH1)
Mcu.Pin5=PC1
Mcu.Pin6=PA1
Mcu.Pin7=PA2
Mcu.Pin8=PA7
Mcu.Pin9=PC4
Mcu.PinsNb=26
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32H743ZITx
MxCube.Version=6.9.2
MxDb.Version=DB.6.0.92
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:false
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA1.Locked=true
PA1.Mode=RMII
PA1.Signal=ETH_REF_CLK
PA11.Locked=true
PA11.Mode=Device_Only
PA11.Signal=USB_OTG_FS_DM
PA12.Locked=true
PA12.Mode=Device_Only
PA12.Signal=USB_OTG_FS_DP
PA2.Locked=true
PA2.Mode=RMII
PA2.Signal=ETH_MDIO
PA7.Locked=true
PA7.Mode=RMII
PA7.Signal=ETH_CRS_DV
PA8.Locked=true
PA8.Mode=Activate_SOF_FS
PA8.Signal=USB_OTG_FS_SOF
PA9.Locked=true
PA9.Mode=Activate_VBUS
PA9.Signal=USB_OTG_FS_VBUS
PB0.GPIOParameters=GPIO_Label
PB0.GPIO_Label=LD1 [Green Led]
PB0.Locked=true
PB0.Signal=GPIO_Output
PB13.Locked=true
PB13.Mode=RMII
PB13.Signal=ETH_TXD1
PB14.GPIOParameters=GPIO_Label
PB14.GPIO_Label=LD3 [Red Led]
PB14.Locked=true
PB14.Signal=GPIO_Output
PC1.Locked=true
PC1.Mode=RMII
PC1.Signal=ETH_MDC
PC13.GPIOParameters=GPIO_Label
PC13.GPIO_Label=B1 [Blue PushButton]
PC13.Locked=true
PC13.Signal=GPIO_Input
PC14-OSC32_IN\ (OSC32_IN).Locked=true
PC14-OSC32_IN\ (OSC32_IN).Mode=LSE-External-Oscillator
PC14-OSC32_IN\ (OSC32_IN).Signal=RCC_OSC32_IN
PC15-OSC32_OUT\ (OSC32_OUT).Locked=true
PC15-OSC32_OUT\ (OSC32_OUT).Mode=LSE-External-Oscillator
PC15-OSC32_OUT\ (OSC32_OUT).Signal=RCC_OSC32_OUT
PC4.Locked=true
PC4.Mode=RMII
PC4.Signal=ETH_RXD0
PC5.Locked=true
PC5.Mode=RMII
PC5.Signal=ETH_RXD1
PD10.GPIOParameters=GPIO_Label
PD10.GPIO_Label=USB_OTG_FS_PWR_EN
PD10.Locked=true
PD10.Signal=GPIO_Output
PD8.GPIOParameters=GPIO_Label
PD8.GPIO_Label=STLINK_RX
PD8.Locked=true
PD8.Mode=Asynchronous
PD8.Signal=USART3_TX
PD9.GPIOParameters=GPIO_Label
PD9.GPIO_Label=STLINK_TX
PD9.Locked=true
PD9.Mode=Asynchronous
PD9.Signal=USART3_RX
PE1.GPIOParameters=GPIO_Label
PE1.GPIO_Label=LD2 [Yellow Led]
PE1.Locked=true
PE1.Signal=GPIO_Output
PG11.Locked=true
PG11.Mode=RMII
PG11.Signal=ETH_TX_EN
PG13.Locked=true
PG13.Mode=RMII
PG13.Signal=ETH_TXD0
PG7.GPIOParameters=GPIO_Label
PG7.GPIO_Label=USB_OTG_FS_OVCR
PG7.Locked=true
PG7.Signal=GPXTI7
PH0-OSC_IN\ (PH0).Locked=true
PH0-OSC_IN\ (PH0).Mode=HSE-External-Clock-Source
PH0-OSC_IN\ (PH0).Signal=RCC_OSC_IN
PH1-OSC_OUT\ (PH1).Locked=true
PH1-OSC_OUT\ (PH1).Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32H743ZITx
ProjectManager.FirmwarePackage=STM32Cube FW_H7 V1.11.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=2
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=STM32CubeIDE
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=stm32h743nucleo.ioc
ProjectManager.ProjectName=stm32h743nucleo
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=Makefile
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,false-3-MX_ETH_Init-ETH-false-HAL-true,4-MX_USART3_UART_Init-USART3-false-HAL-true,5-MX_USB_OTG_FS_PCD_Init-USB_OTG_FS-false-HAL-true,0-MX_CORTEX_M7_Init-CORTEX_M7-false-HAL-true
RCC.ADCFreq_Value=16125000
RCC.AHB12Freq_Value=*********
RCC.AHB4Freq_Value=*********
RCC.APB1Freq_Value=*********
RCC.APB2Freq_Value=*********
RCC.APB3Freq_Value=*********
RCC.APB4Freq_Value=*********
RCC.AXIClockFreq_Value=*********
RCC.CECFreq_Value=32000
RCC.CKPERFreq_Value=64000000
RCC.CortexFreq_Value=*********
RCC.CpuClockFreq_Value=*********
RCC.D1CPREFreq_Value=*********
RCC.D1PPRE=RCC_APB3_DIV2
RCC.D2PPRE1=RCC_APB1_DIV2
RCC.D2PPRE2=RCC_APB2_DIV2
RCC.D3PPRE=RCC_APB4_DIV2
RCC.DFSDMACLkFreq_Value=*********
RCC.DFSDMFreq_Value=*********
RCC.DIVM1=1
RCC.DIVM3=1
RCC.DIVN1=100
RCC.DIVN3=24
RCC.DIVP1Freq_Value=*********
RCC.DIVP2Freq_Value=16125000
RCC.DIVP3Freq_Value=96000000
RCC.DIVQ1=4
RCC.DIVQ1Freq_Value=*********
RCC.DIVQ2Freq_Value=16125000
RCC.DIVQ3=4
RCC.DIVQ3Freq_Value=48000000
RCC.DIVR1Freq_Value=*********
RCC.DIVR2Freq_Value=16125000
RCC.DIVR3Freq_Value=96000000
RCC.FDCANFreq_Value=*********
RCC.FMCFreq_Value=*********
RCC.FamilyName=M
RCC.HCLK3ClockFreq_Value=*********
RCC.HCLKFreq_Value=*********
RCC.HPRE=RCC_HCLK_DIV2
RCC.HRTIMFreq_Value=*********
RCC.HSE_VALUE=8000000
RCC.I2C123Freq_Value=*********
RCC.I2C4Freq_Value=*********
RCC.IPParameters=ADCFreq_Value,AHB12Freq_Value,AHB4Freq_Value,APB1Freq_Value,APB2Freq_Value,APB3Freq_Value,APB4Freq_Value,AXIClockFreq_Value,CECFreq_Value,CKPERFreq_Value,CortexFreq_Value,CpuClockFreq_Value,D1CPREFreq_Value,D1PPRE,D2PPRE1,D2PPRE2,D3PPRE,DFSDMACLkFreq_Value,DFSDMFreq_Value,DIVM1,DIVM3,DIVN1,DIVN3,DIVP1Freq_Value,DIVP2Freq_Value,DIVP3Freq_Value,DIVQ1,DIVQ1Freq_Value,DIVQ2Freq_Value,DIVQ3,DIVQ3Freq_Value,DIVR1Freq_Value,DIVR2Freq_Value,DIVR3Freq_Value,FDCANFreq_Value,FMCFreq_Value,FamilyName,HCLK3ClockFreq_Value,HCLKFreq_Value,HPRE,HRTIMFreq_Value,HSE_VALUE,I2C123Freq_Value,I2C4Freq_Value,LPTIM1Freq_Value,LPTIM2Freq_Value,LPTIM345Freq_Value,LPUART1Freq_Value,LTDCFreq_Value,MCO1PinFreq_Value,MCO2PinFreq_Value,PLL2FRACN,PLL3FRACN,PLLFRACN,PLLSourceVirtual,PWR_Regulator_Voltage_Scale,QSPIFreq_Value,RNGFreq_Value,RTCFreq_Value,SAI1Freq_Value,SAI23Freq_Value,SAI4AFreq_Value,SAI4BFreq_Value,SDMMCFreq_Value,SPDIFRXFreq_Value,SPI123Freq_Value,SPI45Freq_Value,SPI6Freq_Value,SWPMI1Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,Tim1OutputFreq_Value,Tim2OutputFreq_Value,TraceFreq_Value,USART16Freq_Value,USART234578Freq_Value,USBCLockSelection,USBFreq_Value,VCO1OutputFreq_Value,VCO2OutputFreq_Value,VCO3OutputFreq_Value,VCOInput1Freq_Value,VCOInput2Freq_Value,VCOInput3Freq_Value
RCC.LPTIM1Freq_Value=*********
RCC.LPTIM2Freq_Value=*********
RCC.LPTIM345Freq_Value=*********
RCC.LPUART1Freq_Value=*********
RCC.LTDCFreq_Value=96000000
RCC.MCO1PinFreq_Value=64000000
RCC.MCO2PinFreq_Value=*********
RCC.PLL2FRACN=0
RCC.PLL3FRACN=0
RCC.PLLFRACN=0
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.PWR_Regulator_Voltage_Scale=PWR_REGULATOR_VOLTAGE_SCALE1
RCC.QSPIFreq_Value=*********
RCC.RNGFreq_Value=48000000
RCC.RTCFreq_Value=32000
RCC.SAI1Freq_Value=*********
RCC.SAI23Freq_Value=*********
RCC.SAI4AFreq_Value=*********
RCC.SAI4BFreq_Value=*********
RCC.SDMMCFreq_Value=*********
RCC.SPDIFRXFreq_Value=*********
RCC.SPI123Freq_Value=*********
RCC.SPI45Freq_Value=*********
RCC.SPI6Freq_Value=*********
RCC.SWPMI1Freq_Value=*********
RCC.SYSCLKFreq_VALUE=*********
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.Tim1OutputFreq_Value=*********
RCC.Tim2OutputFreq_Value=*********
RCC.TraceFreq_Value=64000000
RCC.USART16Freq_Value=*********
RCC.USART234578Freq_Value=*********
RCC.USBCLockSelection=RCC_USBCLKSOURCE_PLL3
RCC.USBFreq_Value=48000000
RCC.VCO1OutputFreq_Value=*********
RCC.VCO2OutputFreq_Value=32250000
RCC.VCO3OutputFreq_Value=*********
RCC.VCOInput1Freq_Value=8000000
RCC.VCOInput2Freq_Value=250000
RCC.VCOInput3Freq_Value=8000000
SH.GPXTI7.0=GPIO_EXTI7
SH.GPXTI7.ConfNb=1
USART3.IPParameters=VirtualMode-Asynchronous
USART3.VirtualMode-Asynchronous=VM_ASYNC
USB_OTG_FS.IPParameters=VirtualMode
USB_OTG_FS.VirtualMode=Device_Only
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
board=NUCLEO-H743ZI2
boardIOC=true
