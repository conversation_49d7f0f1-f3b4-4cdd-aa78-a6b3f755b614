<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<raConfiguration version="9">
  <generalSettings>
    <option key="#Board#" value="board.ra8m1ek"/>
    <option key="CPU" value="RA8M1"/>
    <option key="Core" value="CM85"/>
    <option key="#TargetName#" value="R7FA8M1AHECBD"/>
    <option key="#TargetARCHITECTURE#" value="cortex-m85"/>
    <option key="#DeviceCommand#" value="R7FA8M1AH"/>
    <option key="#RTOS#" value="_none"/>
    <option key="#pinconfiguration#" value="R7FA8M1AHECBD.pincfg"/>
    <option key="#FSPVersion#" value="5.6.0"/>
    <option key="#ConfigurationFragments#" value="Renesas##BSP##Board##ra8m1_ek##"/>
    <option key="#SELECTED_TOOLCHAIN#" value="com.renesas.cdt.managedbuild.gnuarm.toolchain."/>
  </generalSettings>
  <raBspConfiguration>
    <config id="config.bsp.ra8m1.R7FA8M1AHECBD">
      <property id="config.bsp.part_number" value="config.bsp.part_number.value"/>
      <property id="config.bsp.rom_size_bytes" value="config.bsp.rom_size_bytes.value"/>
      <property id="config.bsp.rom_size_bytes_hidden" value="2064384"/>
      <property id="config.bsp.ram_size_bytes" value="config.bsp.ram_size_bytes.value"/>
      <property id="config.bsp.data_flash_size_bytes" value="config.bsp.data_flash_size_bytes.value"/>
      <property id="config.bsp.package_style" value="config.bsp.package_style.value"/>
      <property id="config.bsp.package_pins" value="config.bsp.package_pins.value"/>
      <property id="config.bsp.irq_count_hidden" value="96"/>
    </config>
    <config id="config.bsp.ra8m1">
      <property id="config.bsp.series" value="config.bsp.series.value"/>
    </config>
    <config id="config.bsp.ra8m1.fsp">
      <property id="config.bsp.fsp.inline_irq_functions" value="config.bsp.common.inline_irq_functions.enabled"/>
      <property id="config.bsp.fsp.sdram.enabled" value="config.bsp.fsp.sdram.enabled.disabled"/>
      <property id="config.bsp.fsp.sdram.tras" value="config.bsp.fsp.sdram.tras.6"/>
      <property id="config.bsp.fsp.sdram.trcd" value="config.bsp.fsp.sdram.trcd.3"/>
      <property id="config.bsp.fsp.sdram.trp" value="config.bsp.fsp.sdram.trp.3"/>
      <property id="config.bsp.fsp.sdram.twr" value="config.bsp.fsp.sdram.twr.2"/>
      <property id="config.bsp.fsp.sdram.tcl" value="config.bsp.fsp.sdram.tcl.3"/>
      <property id="config.bsp.fsp.sdram.trfc" value="937"/>
      <property id="config.bsp.fsp.sdram.trefw" value="config.bsp.fsp.sdram.trefw.8"/>
      <property id="config.bsp.fsp.sdram.arfi" value="config.bsp.fsp.sdram.arfi.10"/>
      <property id="config.bsp.fsp.sdram.arfc" value="config.bsp.fsp.sdram.arfc.8"/>
      <property id="config.bsp.fsp.sdram.prc" value="config.bsp.fsp.sdram.prc.3"/>
      <property id="config.bsp.fsp.sdram.addr_shift" value="config.bsp.fsp.sdram.addr_shift.9"/>
      <property id="config.bsp.fsp.sdram.endian_mode" value="config.bsp.fsp.sdram.endian_mode.little"/>
      <property id="config.bsp.fsp.sdram.continuous_access_mode" value="config.bsp.fsp.sdram.continuous_access_mode.enabled"/>
      <property id="config.bsp.fsp.sdram.bus_width" value="config.bsp.fsp.sdram.bus_width.16"/>
      <property id="config.bsp.fsp.tz.exception_response" value="config.bsp.fsp.tz.exception_response.nmi"/>
      <property id="config.bsp.fsp.tz.cmsis.bfhfnmins" value="config.bsp.fsp.tz.cmsis.bfhfnmins.secure"/>
      <property id="config.bsp.fsp.tz.cmsis.sysresetreqs" value="config.bsp.fsp.tz.cmsis.sysresetreqs.secure_only"/>
      <property id="config.bsp.fsp.tz.cmsis.s_priority_boost" value="config.bsp.fsp.tz.cmsis.s_priority_boost.disabled"/>
      <property id="config.bsp.fsp.tz.rstsar" value="config.bsp.fsp.tz.rstsar.both"/>
      <property id="config.bsp.fsp.tz.bbfsar" value="config.bsp.fsp.tz.bbfsar.both"/>
      <property id="config.bsp.fsp.tz.sramsar.sramsa0" value="config.bsp.fsp.tz.sramsar.sramsa0.both"/>
      <property id="config.bsp.fsp.tz.sramsar.sramsa1" value="config.bsp.fsp.tz.sramsar.sramsa1.both"/>
      <property id="config.bsp.fsp.tz.sramsar.stbramsa" value="config.bsp.fsp.tz.sramsar.stbramsa.both"/>
      <property id="config.bsp.fsp.tz.bussara" value="config.bsp.fsp.tz.bussara.both"/>
      <property id="config.bsp.fsp.tz.bussarb" value="config.bsp.fsp.tz.bussarb.both"/>
      <property id="config.bsp.fsp.tz.bussarc" value="config.bsp.fsp.tz.bussarc.both"/>
      <property id="config.bsp.fsp.tz.banksel_sel" value="config.bsp.fsp.tz.banksel_sel.both"/>
      <property id="config.bsp.fsp.tz.uninitialized_ns_application_fallback" value="config.bsp.fsp.tz.uninitialized_ns_application_fallback.enabled"/>
      <property id="config.bsp.fsp.OFS0.iwdt_start_mode" value="config.bsp.fsp.OFS0.iwdt_start_mode.disabled"/>
      <property id="config.bsp.fsp.OFS0.iwdt_timeout" value="config.bsp.fsp.OFS0.iwdt_timeout.2048"/>
      <property id="config.bsp.fsp.OFS0.iwdt_divisor" value="config.bsp.fsp.OFS0.iwdt_divisor.128"/>
      <property id="config.bsp.fsp.OFS0.iwdt_window_end" value="config.bsp.fsp.OFS0.iwdt_window_end.0"/>
      <property id="config.bsp.fsp.OFS0.iwdt_window_start" value="config.bsp.fsp.OFS0.iwdt_window_start.100"/>
      <property id="config.bsp.fsp.OFS0.iwdt_reset_interrupt" value="config.bsp.fsp.OFS0.iwdt_reset_interrupt.Reset"/>
      <property id="config.bsp.fsp.OFS0.iwdt_stop_control" value="config.bsp.fsp.OFS0.iwdt_stop_control.stops"/>
      <property id="config.bsp.fsp.OFS0.wdt_start_mode" value="config.bsp.fsp.OFS0.wdt_start_mode.register"/>
      <property id="config.bsp.fsp.OFS0.wdt_timeout" value="config.bsp.fsp.OFS0.wdt_timeout.16384"/>
      <property id="config.bsp.fsp.OFS0.wdt_divisor" value="config.bsp.fsp.OFS0.wdt_divisor.128"/>
      <property id="config.bsp.fsp.OFS0.wdt_window_end" value="config.bsp.fsp.OFS0.wdt_window_end.0"/>
      <property id="config.bsp.fsp.OFS0.wdt_window_start" value="config.bsp.fsp.OFS0.wdt_window_start.100"/>
      <property id="config.bsp.fsp.OFS0.wdt_reset_interrupt" value="config.bsp.fsp.OFS0.wdt_reset_interrupt.Reset"/>
      <property id="config.bsp.fsp.OFS0.wdt_stop_control" value="config.bsp.fsp.OFS0.wdt_stop_control.stops"/>
      <property id="config.bsp.fsp.OFS1_SEL.voltage_detection0_level" value="config.bsp.fsp.OFS1_SEL.voltage_detection0_level.secure"/>
      <property id="config.bsp.fsp.OFS1_SEL.voltage_detection0.start" value="config.bsp.fsp.OFS1_SEL.voltage_detection0.start.secure"/>
      <property id="config.bsp.fsp.OFS1_SEL.voltage_detection0.low_power_consumption" value="config.bsp.fsp.OFS1_SEL.voltage_detection0.low_power_consumption.secure"/>
      <property id="config.bsp.fsp.OFS1_SEL.swdbg" value="config.bsp.fsp.OFS1_SEL.swdbg.secure"/>
      <property id="config.bsp.fsp.OFS1_SEL.initeccen" value="config.bsp.fsp.OFS1_SEL.initeccen.secure"/>
      <property id="config.bsp.fsp.OFS1.voltage_detection0.start" value="config.bsp.fsp.OFS1.voltage_detection0.start.disabled"/>
      <property id="config.bsp.fsp.OFS1.voltage_detection0_level" value="config.bsp.fsp.OFS1.voltage_detection0_level.160"/>
      <property id="config.bsp.fsp.OFS1.voltage_detection0.low_power_consumption" value="config.bsp.fsp.OFS1.voltage_detection0.low_power_consumption.disabled"/>
      <property id="config.bsp.fsp.OFS1.hoco_osc" value="config.bsp.fsp.OFS1.hoco_osc.disabled"/>
      <property id="config.bsp.fsp.OFS1.swdbg" value="config.bsp.fsp.OFS1.swdbg.disabled"/>
      <property id="config.bsp.fsp.OFS1.initeccen" value="config.bsp.fsp.OFS1.initeccen.disabled"/>
      <property id="config.bsp.fsp.OFS2.dcdc" value="config.bsp.fsp.OFS2.dcdc.enabled"/>
      <property id="config.bsp.fsp.BPS.BPS0" value=""/>
      <property id="config.bsp.fsp.BPS.BPS1" value=""/>
      <property id="config.bsp.fsp.BPS.BPS2" value=""/>
      <property id="config.bsp.fsp.BPS.BPS3" value=""/>
      <property id="config.bsp.fsp.PBPS.PBPS0" value=""/>
      <property id="config.bsp.fsp.PBPS.PBPS1" value=""/>
      <property id="config.bsp.fsp.PBPS.PBPS2" value=""/>
      <property id="config.bsp.fsp.PBPS.PBPS3" value=""/>
      <property id="config.bsp.fsp.dual_bank" value="config.bsp.fsp.dual_bank.disabled"/>
      <property id="config.bsp.fsp.FSBLCTRL0.FSBLEN" value="config.bsp.fsp.FSBLCTRL0.FSBLEN.disabled"/>
      <property id="config.bsp.fsp.FSBLCTRL0.FSBLSKIPSW" value="config.bsp.fsp.FSBLCTRL0.FSBLSKIPSW.disabled"/>
      <property id="config.bsp.fsp.FSBLCTRL0.FSBLSKIPDS" value="config.bsp.fsp.FSBLCTRL0.FSBLSKIPDS.disabled"/>
      <property id="config.bsp.fsp.FSBLCTRL0.FSBLCLK" value="config.bsp.fsp.FSBLCTRL0.FSBLCLK.240"/>
      <property id="config.bsp.fsp.FSBLCTRL1.FSBLEXMD" value="config.bsp.fsp.FSBLCTRL1.FSBLEXMD.secure_report"/>
      <property id="config.bsp.fsp.FSBLCTRL2.PORTPN" value="config.bsp.fsp.FSBLCTRL2.PORTPN.15"/>
      <property id="config.bsp.fsp.FSBLCTRL2.PORTGN" value="config.bsp.fsp.FSBLCTRL2.PORTGN.reserved"/>
      <property id="config.bsp.fsp.SACC0" value="0xFFFFFFFF"/>
      <property id="config.bsp.fsp.SACC1" value="0xFFFFFFFF"/>
      <property id="config.bsp.fsp.SAMR" value="0xFFFFFFFF"/>
      <property id="config.bsp.fsp.hoco_fll" value="config.bsp.fsp.hoco_fll.disabled"/>
      <property id="config.bsp.fsp.clock_settling_delay" value="config.bsp.fsp.clock_settling_delay.enabled"/>
      <property id="config.bsp.fsp.sleep_mode_delays" value="config.bsp.fsp.sleep_mode_delays.enabled"/>
      <property id="config.bsp.fsp.rtos_idle_sleep" value="config.bsp.fsp.rtos_idle_sleep.disabled"/>
      <property id="config.bsp.fsp.mstp_change_delays" value="config.bsp.fsp.mstp_change_delays.enabled"/>
      <property id="config.bsp.fsp.settling_delay_us" value="150"/>
      <property id="config.bsp.common.main_osc_wait" value="config.bsp.common.main_osc_wait.wait_8163"/>
      <property id="config.bsp.fsp.mcu.adc.max_freq_hz" value="60000000"/>
      <property id="config.bsp.fsp.mcu.sci_b_uart.max_baud" value="30000000"/>
      <property id="config.bsp.fsp.mcu.sci_b_uart.ctspen_channels" value="0x021F"/>
      <property id="config.bsp.fsp.mcu.adc.sample_and_hold" value="1"/>
      <property id="config.bsp.fsp.mcu.adc.sensors_are_exclusive" value="0"/>
      <property id="config.bsp.fsp.mcu.sci_spi.max_bitrate" value="30000000"/>
      <property id="config.bsp.fsp.mcu.spi.max_bitrate" value="60000000"/>
      <property id="config.bsp.fsp.mcu.iic_master.rate.rate_fastplus" value="1"/>
      <property id="config.bsp.fsp.mcu.iic_master.fastplus_channels" value="0x3"/>
      <property id="config.bsp.fsp.mcu.iic_slave.rate.rate_fastplus" value="1"/>
      <property id="config.bsp.fsp.mcu.iic_slave.fastplus_channels" value="0x3"/>
      <property id="config.bsp.fsp.mcu.gpt.pin_count_source_channels" value="0x3FFF"/>
      <property id="config.bsp.fsp.mcu.canfd.num_channels" value="2"/>
      <property id="config.bsp.fsp.mcu.canfd.rx_fifos" value="2"/>
      <property id="config.bsp.fsp.mcu.canfd.buffer_ram" value="1216"/>
      <property id="config.bsp.fsp.mcu.canfd.afl_rules" value="32"/>
      <property id="config.bsp.fsp.mcu.canfd.afl_rules_each_chnl" value="16"/>
      <property id="config.bsp.fsp.mcu.canfd.max_data_rate_hz" value="8"/>
      <property id="config.bsp.fsp.mcu.adc_dmac.samples_per_channel" value="32767"/>
      <property id="config.bsp.fsp.mcu.sci_b_lin.max_baud" value="7500000"/>
      <property id="config.bsp.fsp.dcache" value="config.bsp.fsp.dcache.disabled"/>
    </config>
    <config id="config.bsp.ra">
      <property id="config.bsp.common.main" value="0x1000"/>
      <property id="config.bsp.common.heap" value="0x1000"/>
      <property id="config.bsp.common.vcc" value="3300"/>
      <property id="config.bsp.common.checking" value="config.bsp.common.checking.disabled"/>
      <property id="config.bsp.common.assert" value="config.bsp.common.assert.none"/>
      <property id="config.bsp.common.error_log" value="config.bsp.common.error_log.none"/>
      <property id="config.bsp.common.soft_reset" value="config.bsp.common.soft_reset.disabled"/>
      <property id="config.bsp.common.main_osc_populated" value="config.bsp.common.main_osc_populated.enabled"/>
      <property id="config.bsp.common.pfs_protect" value="config.bsp.common.pfs_protect.enabled"/>
      <property id="config.bsp.common.c_runtime_init" value="config.bsp.common.c_runtime_init.enabled"/>
      <property id="config.bsp.common.early_init" value="config.bsp.common.early_init.disabled"/>
      <property id="config.bsp.common.main_osc_clock_source" value="config.bsp.common.main_osc_clock_source.crystal"/>
      <property id="config.bsp.common.subclock_populated" value="config.bsp.common.subclock_populated.enabled"/>
      <property id="config.bsp.common.subclock_drive" value="config.bsp.common.subclock_drive.standard"/>
      <property id="config.bsp.common.subclock_stabilization_ms" value="1000"/>
    </config>
  </raBspConfiguration>
  <raClockConfiguration>
    <node id="board.clock.xtal.freq" mul="20000000" option="_edit"/>
    <node id="board.clock.hoco.freq" option="board.clock.hoco.freq.48m"/>
    <node id="board.clock.loco.freq" option="board.clock.loco.freq.32768"/>
    <node id="board.clock.moco.freq" option="board.clock.moco.freq.8m"/>
    <node id="board.clock.subclk.freq" option="board.clock.subclk.freq.32768"/>
    <node id="board.clock.pll.source" option="board.clock.pll.source.xtal"/>
    <node id="board.clock.pll.div" option="board.clock.pll.div.2"/>
    <node id="board.clock.pll.mul" option="board.clock.pll.mul.96_00"/>
    <node id="board.clock.pll.display" option="board.clock.pll.display.value"/>
    <node id="board.clock.pll1p.div" option="board.clock.pll1p.div.2"/>
    <node id="board.clock.pll1p.display" option="board.clock.pll1p.display.value"/>
    <node id="board.clock.pll1q.div" option="board.clock.pll1q.div.4"/>
    <node id="board.clock.pll1q.display" option="board.clock.pll1q.display.value"/>
    <node id="board.clock.pll1r.div" option="board.clock.pll1r.div.2"/>
    <node id="board.clock.pll1r.display" option="board.clock.pll1r.display.value"/>
    <node id="board.clock.pll2.source" option="board.clock.pll2.source.disabled"/>
    <node id="board.clock.pll2.div" option="board.clock.pll2.div.2"/>
    <node id="board.clock.pll2.mul" option="board.clock.pll2.mul.96_00"/>
    <node id="board.clock.pll2.display" option="board.clock.pll2.display.value"/>
    <node id="board.clock.pll2p.div" option="board.clock.pll2p.div.2"/>
    <node id="board.clock.pll2p.display" option="board.clock.pll2p.display.value"/>
    <node id="board.clock.pll2q.div" option="board.clock.pll2q.div.2"/>
    <node id="board.clock.pll2q.display" option="board.clock.pll2q.display.value"/>
    <node id="board.clock.pll2r.div" option="board.clock.pll2r.div.2"/>
    <node id="board.clock.pll2r.display" option="board.clock.pll2r.display.value"/>
    <node id="board.clock.clock.source" option="board.clock.clock.source.pll1p"/>
    <node id="board.clock.clkout.source" option="board.clock.clkout.source.disabled"/>
    <node id="board.clock.sciclk.source" option="board.clock.sciclk.source.disabled"/>
    <node id="board.clock.spiclk.source" option="board.clock.spiclk.source.disabled"/>
    <node id="board.clock.canfdclk.source" option="board.clock.canfdclk.source.disabled"/>
    <node id="board.clock.i3cclk.source" option="board.clock.i3cclk.source.disabled"/>
    <node id="board.clock.uck.source" option="board.clock.uck.source.pll1q"/>
    <node id="board.clock.u60ck.source" option="board.clock.u60ck.source.pll1p"/>
    <node id="board.clock.octaspiclk.source" option="board.clock.octaspiclk.source.disabled"/>
    <node id="board.clock.cpuclk.div" option="board.clock.cpuclk.div.1"/>
    <node id="board.clock.iclk.div" option="board.clock.iclk.div.2"/>
    <node id="board.clock.pclka.div" option="board.clock.pclka.div.4"/>
    <node id="board.clock.pclkb.div" option="board.clock.pclkb.div.8"/>
    <node id="board.clock.pclkc.div" option="board.clock.pclkc.div.8"/>
    <node id="board.clock.pclkd.div" option="board.clock.pclkd.div.4"/>
    <node id="board.clock.pclke.div" option="board.clock.pclke.div.2"/>
    <node id="board.clock.sdclkout.enable" option="board.clock.sdclkout.enable.enabled"/>
    <node id="board.clock.bclk.div" option="board.clock.bclk.div.4"/>
    <node id="board.clock.bclkout.div" option="board.clock.bclkout.div.2"/>
    <node id="board.clock.fclk.div" option="board.clock.fclk.div.8"/>
    <node id="board.clock.clkout.div" option="board.clock.clkout.div.1"/>
    <node id="board.clock.sciclk.div" option="board.clock.sciclk.div.4"/>
    <node id="board.clock.spiclk.div" option="board.clock.spiclk.div.4"/>
    <node id="board.clock.canfdclk.div" option="board.clock.canfdclk.div.8"/>
    <node id="board.clock.i3cclk.div" option="board.clock.i3cclk.div.3"/>
    <node id="board.clock.uck.div" option="board.clock.uck.div.5"/>
    <node id="board.clock.u60ck.div" option="board.clock.u60ck.div.8"/>
    <node id="board.clock.octaspiclk.div" option="board.clock.octaspiclk.div.4"/>
    <node id="board.clock.cpuclk.display" option="board.clock.cpuclk.display.value"/>
    <node id="board.clock.iclk.display" option="board.clock.iclk.display.value"/>
    <node id="board.clock.pclka.display" option="board.clock.pclka.display.value"/>
    <node id="board.clock.pclkb.display" option="board.clock.pclkb.display.value"/>
    <node id="board.clock.pclkc.display" option="board.clock.pclkc.display.value"/>
    <node id="board.clock.pclkd.display" option="board.clock.pclkd.display.value"/>
    <node id="board.clock.pclke.display" option="board.clock.pclke.display.value"/>
    <node id="board.clock.sdclkout.display" option="board.clock.sdclkout.display.value"/>
    <node id="board.clock.bclk.display" option="board.clock.bclk.display.value"/>
    <node id="board.clock.bclkout.display" option="board.clock.bclkout.display.value"/>
    <node id="board.clock.fclk.display" option="board.clock.fclk.display.value"/>
    <node id="board.clock.clkout.display" option="board.clock.clkout.display.value"/>
    <node id="board.clock.sciclk.display" option="board.clock.sciclk.display.value"/>
    <node id="board.clock.spiclk.display" option="board.clock.spiclk.display.value"/>
    <node id="board.clock.canfdclk.display" option="board.clock.canfdclk.display.value"/>
    <node id="board.clock.i3cclk.display" option="board.clock.i3cclk.display.value"/>
    <node id="board.clock.uck.display" option="board.clock.uck.display.value"/>
    <node id="board.clock.u60ck.display" option="board.clock.u60ck.display.value"/>
    <node id="board.clock.octaspiclk.display" option="board.clock.octaspiclk.display.value"/>
  </raClockConfiguration>
  <raComponentSelection>
    <component apiversion="" class="Projects" condition="" group="all" subgroup="baremetal_blinky" variant="" vendor="Renesas" version="5.6.0">
      <description>Simple application that blinks an LED. No RTOS included.</description>
      <originalPack>Renesas.RA_baremetal_blinky.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="Common" condition="" group="all" subgroup="fsp_common" variant="" vendor="Renesas" version="5.6.0">
      <description>Board Support Package Common Files</description>
      <originalPack>Renesas.RA.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="HAL Drivers" condition="" group="all" subgroup="r_ioport" variant="" vendor="Renesas" version="5.6.0">
      <description>I/O Port</description>
      <originalPack>Renesas.RA.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="CMSIS" condition="" group="CMSIS5" subgroup="CoreM" variant="" vendor="Arm" version="6.1.0+fsp.5.6.0">
      <description>Arm CMSIS Version 6 - Core (M)</description>
      <originalPack>Arm.CMSIS6.6.1.0+fsp.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="Board" subgroup="ra8m1_ek" variant="" vendor="Renesas" version="5.6.0">
      <description>RA8M1-EK Board Support Files</description>
      <originalPack>Renesas.RA_board_ra8m1_ek.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="ra8m1" subgroup="device" variant="R7FA8M1AHECBD" vendor="Renesas" version="5.6.0">
      <description>Board support package for R7FA8M1AHECBD</description>
      <originalPack>Renesas.RA_mcu_ra8m1.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="ra8m1" subgroup="device" variant="" vendor="Renesas" version="5.6.0">
      <description>Board support package for RA8M1</description>
      <originalPack>Renesas.RA_mcu_ra8m1.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="ra8m1" subgroup="fsp" variant="" vendor="Renesas" version="5.6.0">
      <description>Board support package for RA8M1 - FSP Data</description>
      <originalPack>Renesas.RA_mcu_ra8m1.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="ra8m1" subgroup="events" variant="" vendor="Renesas" version="5.6.0">
      <description>Board support package for RA8M1 - Events</description>
      <originalPack>Renesas.RA_mcu_ra8m1.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="HAL Drivers" condition="" group="all" subgroup="r_usb_basic" variant="" vendor="Renesas" version="5.6.0">
      <description>USB Basic</description>
      <originalPack>Renesas.RA.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="HAL Drivers" condition="" group="all" subgroup="r_usb_pcdc" variant="" vendor="Renesas" version="5.6.0">
      <description>USB Peripheral Communications Device Class</description>
      <originalPack>Renesas.RA.5.6.0.pack</originalPack>
    </component>
  </raComponentSelection>
  <raElcConfiguration/>
  <raIcuConfiguration/>
  <raModuleConfiguration>
    <module id="module.driver.ioport_on_ioport.0">
      <property id="module.driver.ioport.name" value="g_ioport"/>
      <property id="module.driver.ioport.elc_trigger_ioport1" value="_disabled"/>
      <property id="module.driver.ioport.elc_trigger_ioport2" value="_disabled"/>
      <property id="module.driver.ioport.elc_trigger_ioport3" value="_disabled"/>
      <property id="module.driver.ioport.elc_trigger_ioport4" value="_disabled"/>
      <property id="module.driver.ioport.pincfg" value="g_bsp_pin_cfg"/>
    </module>
    <module id="module.driver.pcdc_on_usb.216998869">
      <property id="module.driver.pcdc.name" value="g_pcdc0"/>
    </module>
    <module id="module.driver.basic_on_usb.877233012">
      <property id="module.driver.basic.name" value="g_basic1"/>
      <property id="module.driver.usb_basic.usb_mode" value="module.driver.usb_basic.usb_mode.host"/>
      <property id="module.driver.usb_basic.usb_speed" value="module.driver.usb_basic.usb_speed.hs"/>
      <property id="module.driver.usb_basic.usb_modulenumber" value="module.driver.usb_basic.usb_modulenumber.1"/>
      <property id="module.driver.usb_basic.usb_classtype" value="module.driver.usb_basic.usb_classtype.pcdc"/>
      <property id="module.driver.usb_basic.p_usb_reg" value="g_usb_descriptor"/>
      <property id="module.driver.usb_basic.complience_cb" value="NULL"/>
      <property id="module.driver.usb_basic.ipl" value="board.icu.common.irq.priority12"/>
      <property id="module.driver.usb_basic.ipl_r" value="board.icu.common.irq.priority12"/>
      <property id="module.driver.usb_basic.ipl_d0" value="board.icu.common.irq.priority12"/>
      <property id="module.driver.usb_basic.ipl_d1" value="board.icu.common.irq.priority12"/>
      <property id="module.driver.usb_basic.hsipl" value="board.icu.common.irq.priority12"/>
      <property id="module.driver.usb_basic.hsipl_d0" value="board.icu.common.irq.priority12"/>
      <property id="module.driver.usb_basic.hsipl_d1" value="board.icu.common.irq.priority12"/>
      <property id="module.driver.usb_basic.rtos_callback" value="NULL"/>
      <property id="module.driver.usb_basic.other_context" value="NULL"/>
    </module>
    <context id="_hal.0">
      <stack module="module.driver.ioport_on_ioport.0"/>
      <stack module="module.driver.pcdc_on_usb.216998869">
        <stack module="module.driver.basic_on_usb.877233012" requires="module.driver.basic_on_usb.requires.basic"/>
      </stack>
    </context>
    <config id="config.driver.usb_basic">
      <property id="config.driver.usb_basic.param_checking_enable" value="config.driver.usb_basic.param_checking_enable.bsp"/>
      <property id="config.driver.usb_basic.pll_clock_frequency" value="config.driver.usb_basic.pll_clock_frequency.20mhz"/>
      <property id="config.driver.usb_basic.buswait" value="config.driver.usb_basic.buswait.7"/>
      <property id="config.driver.usb_basic.bc_function" value="config.driver.usb_basic.bc_function.enable"/>
      <property id="config.driver.usb_basic.power_source" value="config.driver.usb_basic.power_source.high"/>
      <property id="config.driver.usb_basic.dcp_function" value="config.driver.usb_basic.dcp_function.disable"/>
      <property id="config.driver.usb_basic.request" value="config.driver.usb_basic.request.enable"/>
      <property id="config.driver.usb_basic.dblb" value="config.driver.usb_basic.dblb.enable"/>
      <property id="config.driver.usb_basic.cntmd" value="config.driver.usb_basic.cntmd.disable"/>
      <property id="config.driver.usb_basic.ldo_regulator" value="config.driver.usb_basic.ldo_regulator.disable"/>
      <property id="config.driver.usb_basic.type_c" value="config.driver.usb_basic.type_c.disable"/>
      <property id="config.driver.usb_basic.dma" value="config.driver.usb_basic.dma.disable"/>
      <property id="config.driver.usb_basic.source_address" value="config.driver.usb_basic.source_address.none"/>
      <property id="config.driver.usb_basic.dest_address" value="config.driver.usb_basic.dest_address.none"/>
      <property id="config.driver.usb_basic.compliance_mode" value="config.driver.usb_basic.compliance_mode.disable"/>
      <property id="config.driver.usb_basic.tpl_table" value="NULL"/>
    </config>
    <config id="config.driver.usb_pcdc">
      <property id="config.driver.usb_pcdc.bulk_in" value="config.driver.usb_pcdc.bulk_in.pipe4"/>
      <property id="config.driver.usb_pcdc.bulk_out" value="config.driver.usb_pcdc.bulk_out.pipe5"/>
      <property id="config.driver.usb_pcdc.int_in" value="config.driver.usb_pcdc.int_in.pipe6"/>
    </config>
    <config id="config.driver.usb_pcdc_class"/>
    <config id="config.driver.ioport">
      <property id="config.driver.ioport.checking" value="config.driver.ioport.checking.system"/>
    </config>
  </raModuleConfiguration>
  <raPinConfiguration>
    <symbolicName propertyId="p000.symbolic_name" value="ENET_RMII_INT"/>
    <symbolicName propertyId="p001.symbolic_name" value="ARDUINO_A3"/>
    <symbolicName propertyId="p002.symbolic_name" value="GROVE2_AN102"/>
    <symbolicName propertyId="p003.symbolic_name" value="ARDUINO_A1"/>
    <symbolicName propertyId="p004.symbolic_name" value="ARDUINO_A0_MIKROBUS_AN000"/>
    <symbolicName propertyId="p005.symbolic_name" value="GROVE2_AN001"/>
    <symbolicName propertyId="p006.symbolic_name" value="PMOD1_IRQ11"/>
    <symbolicName propertyId="p007.symbolic_name" value="ARDUINO_A004"/>
    <symbolicName propertyId="p008.symbolic_name" value="USER_S2"/>
    <symbolicName propertyId="p009.symbolic_name" value="SW1"/>
    <symbolicName propertyId="p010.symbolic_name" value="MIKROBUS_IRQ14"/>
    <symbolicName propertyId="p014.symbolic_name" value="ARDUINO_A4"/>
    <symbolicName propertyId="p015.symbolic_name" value="ARDUINO_A5"/>
    <symbolicName propertyId="p100.symbolic_name" value="OSPI_DQ0"/>
    <symbolicName propertyId="p101.symbolic_name" value="OSPI_DQ3"/>
    <symbolicName propertyId="p102.symbolic_name" value="OSPI_DQ4"/>
    <symbolicName propertyId="p103.symbolic_name" value="OSPI_DQ2"/>
    <symbolicName propertyId="p104.symbolic_name" value="OSPI_CS"/>
    <symbolicName propertyId="p105.symbolic_name" value="OSPI_INT"/>
    <symbolicName propertyId="p106.symbolic_name" value="OSPI_RESET"/>
    <symbolicName propertyId="p107.symbolic_name" value="LED3"/>
    <symbolicName propertyId="p112.symbolic_name" value="ETH_A_RMII_RMII_RXDV"/>
    <symbolicName propertyId="p114.symbolic_name" value="ETH_A_LINKSTA"/>
    <symbolicName propertyId="p115.symbolic_name" value="MPLX_CTRL"/>
    <symbolicName propertyId="p200.symbolic_name" value="NMI"/>
    <symbolicName propertyId="p201.symbolic_name" value="MD"/>
    <symbolicName propertyId="p207.symbolic_name" value="CAN_STB"/>
    <symbolicName propertyId="p208.symbolic_name" value="TDI"/>
    <symbolicName propertyId="p209.symbolic_name" value="TDO"/>
    <symbolicName propertyId="p210.symbolic_name" value="SWDIO"/>
    <symbolicName propertyId="p211.symbolic_name" value="SWCLK"/>
    <symbolicName propertyId="p212.symbolic_name" value="EXTAL"/>
    <symbolicName propertyId="p213.symbolic_name" value="XTAL"/>
    <symbolicName propertyId="p300.symbolic_name" value="ETH_A_RXER"/>
    <symbolicName propertyId="p301.symbolic_name" value="ETH_A_RXD1"/>
    <symbolicName propertyId="p302.symbolic_name" value="ETH_A_RXD0"/>
    <symbolicName propertyId="p303.symbolic_name" value="ETH_A_REFCLK"/>
    <symbolicName propertyId="p304.symbolic_name" value="ETH_A_TXD0"/>
    <symbolicName propertyId="p305.symbolic_name" value="ETH_A_TXD1"/>
    <symbolicName propertyId="p306.symbolic_name" value="ETH_A_TXEN"/>
    <symbolicName propertyId="p307.symbolic_name" value="ETH_A_MDIO"/>
    <symbolicName propertyId="p308.symbolic_name" value="ETH_A_MDC"/>
    <symbolicName propertyId="p309.symbolic_name" value="ARDUINO_D0_MIKROBUS_RXD3"/>
    <symbolicName propertyId="p310.symbolic_name" value="ARDUINO_D1_MIKROBUS_TXD3"/>
    <symbolicName propertyId="p311.symbolic_name" value="CAN_RXD"/>
    <symbolicName propertyId="p312.symbolic_name" value="CAN_TXD"/>
    <symbolicName propertyId="p400.symbolic_name" value="I3C_SCL0_ARDUINO_MIKROBUS_PMOD1_3_qwiic"/>
    <symbolicName propertyId="p401.symbolic_name" value="I3C_SDA0_ARDUINO_MIKROBUS_PMOD1_4_qwiic"/>
    <symbolicName propertyId="p402.symbolic_name" value="ETH_B_MDIO"/>
    <symbolicName propertyId="p403.symbolic_name" value="ETH_B_LINKSTA"/>
    <symbolicName propertyId="p404.symbolic_name" value="ETH_B_RST_N"/>
    <symbolicName propertyId="p405.symbolic_name" value="ETH_B_TXEN"/>
    <symbolicName propertyId="p406.symbolic_name" value="ETH_B_TXD1"/>
    <symbolicName propertyId="p407.symbolic_name" value="USBFS_VBUS"/>
    <symbolicName propertyId="p408.symbolic_name" value="USBHS_VBUSEN"/>
    <symbolicName propertyId="p409.symbolic_name" value="USBHS_OVRCURA"/>
    <symbolicName propertyId="p410.symbolic_name" value="MISOB_B_ARDUINO_MIKROBUS"/>
    <symbolicName propertyId="p411.symbolic_name" value="MOSIB_B_ARDUINO_MIKROBUS"/>
    <symbolicName propertyId="p412.symbolic_name" value="RSPCKB_B_ARDUINO_MIKROBUS"/>
    <symbolicName propertyId="p413.symbolic_name" value="SSLB0_B_ARDUINO_D10_MIKROBUS"/>
    <symbolicName propertyId="p414.symbolic_name" value="LED2"/>
    <symbolicName propertyId="p500.symbolic_name" value="USBFS_VBUS_EN"/>
    <symbolicName propertyId="p501.symbolic_name" value="USBFS_OVERCURA"/>
    <symbolicName propertyId="p502.symbolic_name" value="MIKROBUS_RESET"/>
    <symbolicName propertyId="p508.symbolic_name" value="PMOD2_7_IRQ1"/>
    <symbolicName propertyId="p511.symbolic_name" value="GROVE2_IIC_SDA1"/>
    <symbolicName propertyId="p512.symbolic_name" value="GROVE2_IIC_SCL1"/>
    <symbolicName propertyId="p600.symbolic_name" value="LED1"/>
    <symbolicName propertyId="p601.symbolic_name" value="ARDUINO_D5"/>
    <symbolicName propertyId="p602.symbolic_name" value="ARDUINO_D6"/>
    <symbolicName propertyId="p603.symbolic_name" value="ARDUINO_D9"/>
    <symbolicName propertyId="p609.symbolic_name" value="PMOD1_3_MISO0_RXD0_SCL0"/>
    <symbolicName propertyId="p610.symbolic_name" value="PMOD1_2_MOSI0_TXD0"/>
    <symbolicName propertyId="p611.symbolic_name" value="PMOD1_4_SCK0"/>
    <symbolicName propertyId="p612.symbolic_name" value="PMOD1_1_SSL0_CTS_RTS"/>
    <symbolicName propertyId="p613.symbolic_name" value="PMOD1_1_CTS0"/>
    <symbolicName propertyId="p614.symbolic_name" value="PMOD1_9_GPIO"/>
    <symbolicName propertyId="p615.symbolic_name" value="PMOD1_10_GPIO"/>
    <symbolicName propertyId="p700.symbolic_name" value="ETH_B_TXD0"/>
    <symbolicName propertyId="p701.symbolic_name" value="ETH_B_REFCLK"/>
    <symbolicName propertyId="p702.symbolic_name" value="ETH_B_RXD0"/>
    <symbolicName propertyId="p703.symbolic_name" value="ETH_B_RXD1"/>
    <symbolicName propertyId="p704.symbolic_name" value="ETH_B_RXER"/>
    <symbolicName propertyId="p705.symbolic_name" value="ETH_B_RMII_RXDV"/>
    <symbolicName propertyId="p711.symbolic_name" value="I3C_SDA0_PULLUP"/>
    <symbolicName propertyId="p800.symbolic_name" value="OSPI_DQ5"/>
    <symbolicName propertyId="p801.symbolic_name" value="OSPI_DS"/>
    <symbolicName propertyId="p802.symbolic_name" value="OSPI_DQ6"/>
    <symbolicName propertyId="p803.symbolic_name" value="OSPI_DQ1"/>
    <symbolicName propertyId="p804.symbolic_name" value="OSPI_DQ7"/>
    <symbolicName propertyId="p808.symbolic_name" value="OSPI_CK"/>
    <symbolicName propertyId="p809.symbolic_name" value="PMOD2_8_RESET"/>
    <symbolicName propertyId="p810.symbolic_name" value="PMOD2_9_GPIO"/>
    <symbolicName propertyId="p811.symbolic_name" value="PMOD2_10_GPIO"/>
    <symbolicName propertyId="p812.symbolic_name" value="ARDUINO_RESET"/>
    <symbolicName propertyId="p814.symbolic_name" value="USBFS_P"/>
    <symbolicName propertyId="p815.symbolic_name" value="USBFS_N"/>
    <symbolicName propertyId="p905.symbolic_name" value="ARDUINO_D4"/>
    <symbolicName propertyId="p906.symbolic_name" value="ARDUINO_D2"/>
    <symbolicName propertyId="p907.symbolic_name" value="ARDUINO_D3_MIKROBUS_GTIOC13A"/>
    <symbolicName propertyId="p908.symbolic_name" value="ARDUINO_D7"/>
    <symbolicName propertyId="p909.symbolic_name" value="ARDUINO_D8"/>
    <symbolicName propertyId="pa02.symbolic_name" value="PMOD2_3_MISO2_RXD2"/>
    <symbolicName propertyId="pa03.symbolic_name" value="PMOD2_2_MOSI2_TXD2"/>
    <symbolicName propertyId="pa04.symbolic_name" value="PMOD2_4_SCK2"/>
    <symbolicName propertyId="pa05.symbolic_name" value="PMOD2_1_CTS_RTS_SSL2"/>
    <symbolicName propertyId="pa06.symbolic_name" value="PMOD2_1_CTS2"/>
    <symbolicName propertyId="pa08.symbolic_name" value="PMOD1_8_RESET"/>
    <symbolicName propertyId="pa14.symbolic_name" value="JLOB_COMS_TX"/>
    <symbolicName propertyId="pa15.symbolic_name" value="JLOB_COMS_RX"/>
    <symbolicName propertyId="pb00.symbolic_name" value="I3C_SCL0_PULLUP"/>
    <symbolicName propertyId="pb01.symbolic_name" value="USBHS_VBUS"/>
    <pincfg active="true" name="RA8M1 EK" selected="true" symbol="g_bsp_pin_cfg">
      <configSetting altId="adc0.an000.p004" configurationId="adc0.an000"/>
      <configSetting altId="adc0.an001.p005" configurationId="adc0.an001"/>
      <configSetting altId="adc0.an004.p007" configurationId="adc0.an004"/>
      <configSetting altId="adc0.an007.p014" configurationId="adc0.an007"/>
      <configSetting altId="adc0.mode.custom.free" configurationId="adc0.mode"/>
      <configSetting altId="adc1.an102.p002" configurationId="adc1.an102"/>
      <configSetting altId="adc1.an104.p003" configurationId="adc1.an104"/>
      <configSetting altId="adc1.an105.p015" configurationId="adc1.an105"/>
      <configSetting altId="adc1.an106.p011" configurationId="adc1.an106"/>
      <configSetting altId="adc1.mode.custom.free" configurationId="adc1.mode"/>
      <configSetting altId="ether_rmii.pairing.a" configurationId="ether_rmii.pairing"/>
      <configSetting altId="iic1.mode.enabled.a" configurationId="iic1.mode"/>
      <configSetting altId="iic1.scl1.p512" configurationId="iic1.scl1"/>
      <configSetting altId="iic1.sda1.p511" configurationId="iic1.sda1"/>
      <configSetting altId="irq12.irq12_dash_ds.p008" configurationId="irq12.irq12_dash_ds"/>
      <configSetting altId="irq12.mode.custom.free" configurationId="irq12.mode"/>
      <configSetting altId="irq13.irq13_dash_ds.p009" configurationId="irq13.irq13_dash_ds"/>
      <configSetting altId="irq13.mode.custom.free" configurationId="irq13.mode"/>
      <configSetting altId="irq9.mode.custom.free" configurationId="irq9.mode"/>
      <configSetting altId="jtag_fslash_swd.mode.swd.free" configurationId="jtag_fslash_swd.mode"/>
      <configSetting altId="jtag_fslash_swd.swclk.p211" configurationId="jtag_fslash_swd.swclk"/>
      <configSetting altId="jtag_fslash_swd.swdio.p210" configurationId="jtag_fslash_swd.swdio"/>
      <configSetting altId="ospi.mode.custom.free" configurationId="ospi.mode"/>
      <configSetting altId="ospi.om_cs1.p104" configurationId="ospi.om_cs1"/>
      <configSetting altId="ospi.om_dqs.p801" configurationId="ospi.om_dqs"/>
      <configSetting altId="ospi.om_ecsint1.p105" configurationId="ospi.om_ecsint1"/>
      <configSetting altId="ospi.om_reset.p106" configurationId="ospi.om_reset"/>
      <configSetting altId="ospi.om_sclk.p808" configurationId="ospi.om_sclk"/>
      <configSetting altId="ospi.om_sio0.p100" configurationId="ospi.om_sio0"/>
      <configSetting altId="ospi.om_sio1.p803" configurationId="ospi.om_sio1"/>
      <configSetting altId="ospi.om_sio2.p103" configurationId="ospi.om_sio2"/>
      <configSetting altId="ospi.om_sio3.p101" configurationId="ospi.om_sio3"/>
      <configSetting altId="ospi.om_sio4.p102" configurationId="ospi.om_sio4"/>
      <configSetting altId="ospi.om_sio5.p800" configurationId="ospi.om_sio5"/>
      <configSetting altId="ospi.om_sio6.p802" configurationId="ospi.om_sio6"/>
      <configSetting altId="ospi.om_sio7.p804" configurationId="ospi.om_sio7"/>
      <configSetting altId="p000.input" configurationId="p000"/>
      <configSetting altId="p000.gpio_mode.gpio_mode_in" configurationId="p000.gpio_mode"/>
      <configSetting altId="p002.adc1.an102" configurationId="p002"/>
      <configSetting altId="p002.gpio_mode.gpio_mode_an" configurationId="p002.gpio_mode"/>
      <configSetting altId="p003.adc1.an104" configurationId="p003"/>
      <configSetting altId="p003.gpio_mode.gpio_mode_an" configurationId="p003.gpio_mode"/>
      <configSetting altId="p004.adc0.an000" configurationId="p004"/>
      <configSetting altId="p004.gpio_mode.gpio_mode_an" configurationId="p004.gpio_mode"/>
      <configSetting altId="p005.adc0.an001" configurationId="p005"/>
      <configSetting altId="p005.gpio_mode.gpio_mode_an" configurationId="p005.gpio_mode"/>
      <configSetting altId="p007.adc0.an004" configurationId="p007"/>
      <configSetting altId="p007.gpio_mode.gpio_mode_an" configurationId="p007.gpio_mode"/>
      <configSetting altId="p008.irq12.irq12_dash_ds" configurationId="p008"/>
      <configSetting altId="p008.gpio_irq.gpio_irq_enabled" configurationId="p008.gpio_irq"/>
      <configSetting altId="p008.gpio_mode.gpio_mode_irq" configurationId="p008.gpio_mode"/>
      <configSetting altId="p009.irq13.irq13_dash_ds" configurationId="p009"/>
      <configSetting altId="p009.gpio_irq.gpio_irq_enabled" configurationId="p009.gpio_irq"/>
      <configSetting altId="p009.gpio_mode.gpio_mode_irq" configurationId="p009.gpio_mode"/>
      <configSetting altId="p011.adc1.an106" configurationId="p011"/>
      <configSetting altId="p011.gpio_mode.gpio_mode_an" configurationId="p011.gpio_mode"/>
      <configSetting altId="p014.adc0.an007" configurationId="p014"/>
      <configSetting altId="p014.gpio_mode.gpio_mode_an" configurationId="p014.gpio_mode"/>
      <configSetting altId="p015.adc1.an105" configurationId="p015"/>
      <configSetting altId="p015.gpio_mode.gpio_mode_an" configurationId="p015.gpio_mode"/>
      <configSetting altId="p100.ospi.om_sio0" configurationId="p100"/>
      <configSetting altId="p100.gpio_speed.gpio_speed_hh" configurationId="p100.gpio_drivecapacity"/>
      <configSetting altId="p100.gpio_mode.gpio_mode_peripheral" configurationId="p100.gpio_mode"/>
      <configSetting altId="p101.ospi.om_sio3" configurationId="p101"/>
      <configSetting altId="p101.gpio_speed.gpio_speed_hh" configurationId="p101.gpio_drivecapacity"/>
      <configSetting altId="p101.gpio_mode.gpio_mode_peripheral" configurationId="p101.gpio_mode"/>
      <configSetting altId="p102.ospi.om_sio4" configurationId="p102"/>
      <configSetting altId="p102.gpio_speed.gpio_speed_hh" configurationId="p102.gpio_drivecapacity"/>
      <configSetting altId="p102.gpio_mode.gpio_mode_peripheral" configurationId="p102.gpio_mode"/>
      <configSetting altId="p103.ospi.om_sio2" configurationId="p103"/>
      <configSetting altId="p103.gpio_speed.gpio_speed_hh" configurationId="p103.gpio_drivecapacity"/>
      <configSetting altId="p103.gpio_mode.gpio_mode_peripheral" configurationId="p103.gpio_mode"/>
      <configSetting altId="p104.ospi.om_cs1" configurationId="p104"/>
      <configSetting altId="p104.gpio_speed.gpio_speed_h" configurationId="p104.gpio_drivecapacity"/>
      <configSetting altId="p104.gpio_mode.gpio_mode_peripheral" configurationId="p104.gpio_mode"/>
      <configSetting altId="p105.ospi.om_ecsint1" configurationId="p105"/>
      <configSetting altId="p105.gpio_mode.gpio_mode_peripheral" configurationId="p105.gpio_mode"/>
      <configSetting altId="p106.ospi.om_reset" configurationId="p106"/>
      <configSetting altId="p106.gpio_mode.gpio_mode_peripheral" configurationId="p106.gpio_mode"/>
      <configSetting altId="p107.output.low" configurationId="p107"/>
      <configSetting altId="p107.gpio_mode.gpio_mode_out.low" configurationId="p107.gpio_mode"/>
      <configSetting altId="p209.trace.traceswo" configurationId="p209"/>
      <configSetting altId="p209.gpio_mode.gpio_mode_peripheral" configurationId="p209.gpio_mode"/>
      <configSetting altId="p210.jtag_fslash_swd.swdio" configurationId="p210"/>
      <configSetting altId="p210.gpio_mode.gpio_mode_peripheral" configurationId="p210.gpio_mode"/>
      <configSetting altId="p211.jtag_fslash_swd.swclk" configurationId="p211"/>
      <configSetting altId="p211.gpio_mode.gpio_mode_peripheral" configurationId="p211.gpio_mode"/>
      <configSetting altId="p304.trace.tdata3" configurationId="p304"/>
      <configSetting altId="p304.gpio_mode.gpio_mode_peripheral" configurationId="p304.gpio_mode"/>
      <configSetting altId="p305.trace.tdata2" configurationId="p305"/>
      <configSetting altId="p305.gpio_mode.gpio_mode_peripheral" configurationId="p305.gpio_mode"/>
      <configSetting altId="p306.trace.tdata1" configurationId="p306"/>
      <configSetting altId="p306.gpio_mode.gpio_mode_peripheral" configurationId="p306.gpio_mode"/>
      <configSetting altId="p307.trace.tdata0" configurationId="p307"/>
      <configSetting altId="p307.gpio_mode.gpio_mode_peripheral" configurationId="p307.gpio_mode"/>
      <configSetting altId="p308.trace.tclk" configurationId="p308"/>
      <configSetting altId="p308.gpio_mode.gpio_mode_peripheral" configurationId="p308.gpio_mode"/>
      <configSetting altId="p407.usbfs.usb_vbus" configurationId="p407"/>
      <configSetting altId="p407.gpio_mode.gpio_mode_peripheral" configurationId="p407.gpio_mode"/>
      <configSetting altId="p408.usbhs.usbhs_vbusen" configurationId="p408"/>
      <configSetting altId="p408.gpio_mode.gpio_mode_peripheral" configurationId="p408.gpio_mode"/>
      <configSetting altId="p409.usbhs.usbhs_ovrcura" configurationId="p409"/>
      <configSetting altId="p409.gpio_mode.gpio_mode_peripheral" configurationId="p409.gpio_mode"/>
      <configSetting altId="p410.spi1.miso1" configurationId="p410"/>
      <configSetting altId="p410.gpio_speed.gpio_speed_h" configurationId="p410.gpio_drivecapacity"/>
      <configSetting altId="p410.gpio_mode.gpio_mode_peripheral" configurationId="p410.gpio_mode"/>
      <configSetting altId="p411.spi1.mosi1" configurationId="p411"/>
      <configSetting altId="p411.gpio_speed.gpio_speed_h" configurationId="p411.gpio_drivecapacity"/>
      <configSetting altId="p411.gpio_mode.gpio_mode_peripheral" configurationId="p411.gpio_mode"/>
      <configSetting altId="p412.spi1.rspck1" configurationId="p412"/>
      <configSetting altId="p412.gpio_speed.gpio_speed_h" configurationId="p412.gpio_drivecapacity"/>
      <configSetting altId="p412.gpio_mode.gpio_mode_peripheral" configurationId="p412.gpio_mode"/>
      <configSetting altId="p413.spi1.sslb0" configurationId="p413"/>
      <configSetting altId="p413.gpio_speed.gpio_speed_h" configurationId="p413.gpio_drivecapacity"/>
      <configSetting altId="p413.gpio_mode.gpio_mode_peripheral" configurationId="p413.gpio_mode"/>
      <configSetting altId="p414.output.low" configurationId="p414"/>
      <configSetting altId="p414.gpio_mode.gpio_mode_out.low" configurationId="p414.gpio_mode"/>
      <configSetting altId="p500.usbfs.usb_vbusen" configurationId="p500"/>
      <configSetting altId="p500.gpio_mode.gpio_mode_peripheral" configurationId="p500.gpio_mode"/>
      <configSetting altId="p501.usbfs.usb_ovrcura" configurationId="p501"/>
      <configSetting altId="p501.gpio_mode.gpio_mode_peripheral" configurationId="p501.gpio_mode"/>
      <configSetting altId="p511.iic1.sda1" configurationId="p511"/>
      <configSetting altId="p511.gpio_speed.gpio_speed_m" configurationId="p511.gpio_drivecapacity"/>
      <configSetting altId="p511.gpio_mode.gpio_mode_peripheral" configurationId="p511.gpio_mode"/>
      <configSetting altId="p512.iic1.scl1" configurationId="p512"/>
      <configSetting altId="p512.gpio_speed.gpio_speed_m" configurationId="p512.gpio_drivecapacity"/>
      <configSetting altId="p512.gpio_mode.gpio_mode_peripheral" configurationId="p512.gpio_mode"/>
      <configSetting altId="p600.output.low" configurationId="p600"/>
      <configSetting altId="p600.gpio_mode.gpio_mode_out.low" configurationId="p600.gpio_mode"/>
      <configSetting altId="p800.ospi.om_sio5" configurationId="p800"/>
      <configSetting altId="p800.gpio_speed.gpio_speed_hh" configurationId="p800.gpio_drivecapacity"/>
      <configSetting altId="p800.gpio_mode.gpio_mode_peripheral" configurationId="p800.gpio_mode"/>
      <configSetting altId="p801.ospi.om_dqs" configurationId="p801"/>
      <configSetting altId="p801.gpio_speed.gpio_speed_hh" configurationId="p801.gpio_drivecapacity"/>
      <configSetting altId="p801.gpio_mode.gpio_mode_peripheral" configurationId="p801.gpio_mode"/>
      <configSetting altId="p802.ospi.om_sio6" configurationId="p802"/>
      <configSetting altId="p802.gpio_speed.gpio_speed_hh" configurationId="p802.gpio_drivecapacity"/>
      <configSetting altId="p802.gpio_mode.gpio_mode_peripheral" configurationId="p802.gpio_mode"/>
      <configSetting altId="p803.ospi.om_sio1" configurationId="p803"/>
      <configSetting altId="p803.gpio_speed.gpio_speed_hh" configurationId="p803.gpio_drivecapacity"/>
      <configSetting altId="p803.gpio_mode.gpio_mode_peripheral" configurationId="p803.gpio_mode"/>
      <configSetting altId="p804.ospi.om_sio7" configurationId="p804"/>
      <configSetting altId="p804.gpio_speed.gpio_speed_hh" configurationId="p804.gpio_drivecapacity"/>
      <configSetting altId="p804.gpio_mode.gpio_mode_peripheral" configurationId="p804.gpio_mode"/>
      <configSetting altId="p808.ospi.om_sclk" configurationId="p808"/>
      <configSetting altId="p808.gpio_speed.gpio_speed_hh" configurationId="p808.gpio_drivecapacity"/>
      <configSetting altId="p808.gpio_mode.gpio_mode_peripheral" configurationId="p808.gpio_mode"/>
      <configSetting altId="p809.output.low" configurationId="p809"/>
      <configSetting altId="p809.gpio_mode.gpio_mode_out.low" configurationId="p809.gpio_mode"/>
      <configSetting altId="p814.usbfs.usb_dp" configurationId="p814"/>
      <configSetting altId="p814.gpio_mode.gpio_mode_peripheral" configurationId="p814.gpio_mode"/>
      <configSetting altId="p815.usbfs.usb_dm" configurationId="p815"/>
      <configSetting altId="p815.gpio_mode.gpio_mode_peripheral" configurationId="p815.gpio_mode"/>
      <configSetting altId="pa02.sci2.rxd2" configurationId="pa02"/>
      <configSetting altId="pa02.gpio_speed.gpio_speed_h" configurationId="pa02.gpio_drivecapacity"/>
      <configSetting altId="pa02.gpio_mode.gpio_mode_peripheral" configurationId="pa02.gpio_mode"/>
      <configSetting altId="pa03.sci2.txd2" configurationId="pa03"/>
      <configSetting altId="pa03.gpio_speed.gpio_speed_h" configurationId="pa03.gpio_drivecapacity"/>
      <configSetting altId="pa03.gpio_mode.gpio_mode_peripheral" configurationId="pa03.gpio_mode"/>
      <configSetting altId="pa04.sci2.sck2" configurationId="pa04"/>
      <configSetting altId="pa04.gpio_speed.gpio_speed_h" configurationId="pa04.gpio_drivecapacity"/>
      <configSetting altId="pa04.gpio_mode.gpio_mode_peripheral" configurationId="pa04.gpio_mode"/>
      <configSetting altId="pa05.sci2.cts_rts2" configurationId="pa05"/>
      <configSetting altId="pa05.gpio_speed.gpio_speed_h" configurationId="pa05.gpio_drivecapacity"/>
      <configSetting altId="pa05.gpio_mode.gpio_mode_peripheral" configurationId="pa05.gpio_mode"/>
      <configSetting altId="pa06.input" configurationId="pa06"/>
      <configSetting altId="pa06.gpio_mode.gpio_mode_in" configurationId="pa06.gpio_mode"/>
      <configSetting altId="pa14.sci9.txd9" configurationId="pa14"/>
      <configSetting altId="pa14.gpio_speed.gpio_speed_h" configurationId="pa14.gpio_drivecapacity"/>
      <configSetting altId="pa14.gpio_mode.gpio_mode_peripheral" configurationId="pa14.gpio_mode"/>
      <configSetting altId="pa15.sci9.rxd9" configurationId="pa15"/>
      <configSetting altId="pa15.gpio_speed.gpio_speed_h" configurationId="pa15.gpio_drivecapacity"/>
      <configSetting altId="pa15.gpio_mode.gpio_mode_peripheral" configurationId="pa15.gpio_mode"/>
      <configSetting altId="pb01.usbhs.usbhs_vbus" configurationId="pb01"/>
      <configSetting altId="pb01.gpio_speed.gpio_speed_h" configurationId="pb01.gpio_drivecapacity"/>
      <configSetting altId="pb01.gpio_mode.gpio_mode_peripheral" configurationId="pb01.gpio_mode"/>
      <configSetting altId="sci0.mode.custom.free" configurationId="sci0.mode"/>
      <configSetting altId="sci1.mode.custom.free" configurationId="sci1.mode"/>
      <configSetting altId="sci2.cts_rts2.pa05" configurationId="sci2.cts_rts2"/>
      <configSetting altId="sci2.mode.custom.free" configurationId="sci2.mode"/>
      <configSetting altId="sci2.rxd2.pa02" configurationId="sci2.rxd2"/>
      <configSetting altId="sci2.sck2.pa04" configurationId="sci2.sck2"/>
      <configSetting altId="sci2.txd2.pa03" configurationId="sci2.txd2"/>
      <configSetting altId="sci4.mode.custom.free" configurationId="sci4.mode"/>
      <configSetting altId="sci9.mode.custom.free" configurationId="sci9.mode"/>
      <configSetting altId="sci9.rxd9.pa15" configurationId="sci9.rxd9"/>
      <configSetting altId="sci9.txd9.pa14" configurationId="sci9.txd9"/>
      <configSetting altId="spi1.miso1.p410" configurationId="spi1.miso1"/>
      <configSetting altId="spi1.mode.custom.free" configurationId="spi1.mode"/>
      <configSetting altId="spi1.mosi1.p411" configurationId="spi1.mosi1"/>
      <configSetting altId="spi1.rspck1.p412" configurationId="spi1.rspck1"/>
      <configSetting altId="spi1.sslb0.p413" configurationId="spi1.sslb0"/>
      <configSetting altId="system.mode.custom.free" configurationId="system.mode"/>
      <configSetting altId="trace.mode.custom.free" configurationId="trace.mode"/>
      <configSetting altId="trace.tclk.p308" configurationId="trace.tclk"/>
      <configSetting altId="trace.tdata0.p307" configurationId="trace.tdata0"/>
      <configSetting altId="trace.tdata1.p306" configurationId="trace.tdata1"/>
      <configSetting altId="trace.tdata2.p305" configurationId="trace.tdata2"/>
      <configSetting altId="trace.tdata3.p304" configurationId="trace.tdata3"/>
      <configSetting altId="trace.traceswo.p209" configurationId="trace.traceswo"/>
      <configSetting altId="usbfs.mode.custom.free" configurationId="usbfs.mode"/>
      <configSetting altId="usbfs.usb_dm.p815" configurationId="usbfs.usb_dm"/>
      <configSetting altId="usbfs.usb_dp.p814" configurationId="usbfs.usb_dp"/>
      <configSetting altId="usbfs.usb_ovrcura.p501" configurationId="usbfs.usb_ovrcura"/>
      <configSetting altId="usbfs.usb_vbus.p407" configurationId="usbfs.usb_vbus"/>
      <configSetting altId="usbfs.usb_vbusen.p500" configurationId="usbfs.usb_vbusen"/>
      <configSetting altId="usbhs.mode.custom.free" configurationId="usbhs.mode"/>
      <configSetting altId="usbhs.usbhs_ovrcura.p409" configurationId="usbhs.usbhs_ovrcura"/>
      <configSetting altId="usbhs.usbhs_vbus.pb01" configurationId="usbhs.usbhs_vbus"/>
      <configSetting altId="usbhs.usbhs_vbusen.p408" configurationId="usbhs.usbhs_vbusen"/>
    </pincfg>
    <pincfg active="false" name="R7FA8M1AHECBD.pincfg" selected="false" symbol="">
      <configSetting altId="jtag_fslash_swd.mode.jtag.free" configurationId="jtag_fslash_swd.mode"/>
      <configSetting altId="jtag_fslash_swd.tck.p211" configurationId="jtag_fslash_swd.tck"/>
      <configSetting altId="jtag_fslash_swd.tdi.p208" configurationId="jtag_fslash_swd.tdi"/>
      <configSetting altId="jtag_fslash_swd.tdo.p209" configurationId="jtag_fslash_swd.tdo"/>
      <configSetting altId="jtag_fslash_swd.tms.p210" configurationId="jtag_fslash_swd.tms"/>
      <configSetting altId="p208.jtag_fslash_swd.tdi" configurationId="p208"/>
      <configSetting altId="p208.gpio_mode.gpio_mode_peripheral" configurationId="p208.gpio_mode"/>
      <configSetting altId="p209.jtag_fslash_swd.tdo" configurationId="p209"/>
      <configSetting altId="p209.gpio_mode.gpio_mode_peripheral" configurationId="p209.gpio_mode"/>
      <configSetting altId="p210.jtag_fslash_swd.tms" configurationId="p210"/>
      <configSetting altId="p210.gpio_mode.gpio_mode_peripheral" configurationId="p210.gpio_mode"/>
      <configSetting altId="p211.jtag_fslash_swd.tck" configurationId="p211"/>
      <configSetting altId="p211.gpio_mode.gpio_mode_peripheral" configurationId="p211.gpio_mode"/>
    </pincfg>
  </raPinConfiguration>
</raConfiguration>
