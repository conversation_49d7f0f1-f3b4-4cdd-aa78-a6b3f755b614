|                            | BCM2711 (Pi4)   | EFM32GG      | ESP32-S2/S3   | ESP32-P4     | ST F207/F407/411/429 FS   | ST F407/429 HS   | ST F412/76x FS   | ST F723/L4P5 FS   | ST F723 HS   | ST F76x HS   | ST H743/H750   | ST L476 FS   | ST U5A5 HS   | XMC4500      | GD32VF103   |
|:---------------------------|:----------------|:-------------|:--------------|:-------------|:--------------------------|:-----------------|:-----------------|:------------------|:-------------|:-------------|:---------------|:-------------|:-------------|:-------------|:------------|
| GUID                       | 0x2708A000      | 0x00000000   | 0x00000000    | 0x00000000   | 0x00001200                | 0x00001100       | 0x00002000       | 0x00003000        | 0x00003100   | 0x00002100   | 0x00002300     | 0x00002000   | 0x00005000   | 0x00AEC000   | 0x00001000  |
| GSNPSID                    | 0x4F54280A      | 0x4F54330A   | 0x4F54400A    | 0x4F54400A   | 0x4F54281A                | 0x4F54281A       | 0x4F54320A       | 0x4F54330A        | 0x4F54330A   | 0x4F54320A   | 0x4F54330A     | 0x4F54310A   | 0x4F54411A   | 0x4F54292A   | 0x00000000  |
| - specs version            | 2.80a           | 3.30a        | 4.00a         | 4.00a        | 2.81a                     | 2.81a            | 3.20a            | 3.30a             | 3.30a        | 3.20a        | 3.30a          | 3.10a        | 4.11a        | 2.92a        | 0.00W       |
| GHWCFG1                    | 0x00000000      | 0x00000000   | 0x00000000    | 0x00000000   | 0x00000000                | 0x00000000       | 0x00000000       | 0x00000000        | 0x00000000   | 0x00000000   | 0x00000000     | 0x00000000   | 0x00000000   | 0x00000000   | 0x00000000  |
| GHWCFG2                    | 0x228DDD50      | 0x228F5910   | 0x224DD930    | 0x215FFFD0   | 0x229DCD20                | 0x229ED590       | 0x229ED520       | 0x229ED520        | 0x229FE1D0   | 0x229FE190   | 0x229FE190     | 0x229ED520   | 0x228FE052   | 0x228F5930   | 0x00000000  |
| - op_mode                  | HNP SRP         | HNP SRP      | HNP SRP       | HNP SRP      | HNP SRP                   | HNP SRP          | HNP SRP          | HNP SRP           | HNP SRP      | HNP SRP      | HNP SRP        | HNP SRP      | noHNP noSRP  | HNP SRP      | HNP SRP     |
| - arch                     | DMA internal    | DMA internal | DMA internal  | DMA internal | Slave only                | DMA internal     | Slave only       | Slave only        | DMA internal | DMA internal | DMA internal   | Slave only   | DMA internal | DMA internal | Slave only  |
| - single_point             | hub             | hub          | n/a           | hub          | n/a                       | hub              | n/a              | n/a               | hub          | hub          | hub            | n/a          | hub          | n/a          | hub         |
| - hs_phy_type              | UTMI+           | n/a          | n/a           | UTMI+/ULPI   | n/a                       | ULPI             | n/a              | n/a               | UTMI+/ULPI   | ULPI         | ULPI           | n/a          | UTMI+        | n/a          | n/a         |
| - fs_phy_type              | Dedicated       | Dedicated    | Dedicated     | Shared ULPI  | Dedicated                 | Dedicated        | Dedicated        | Dedicated         | Dedicated    | Dedicated    | Dedicated      | Dedicated    | n/a          | Dedicated    | n/a         |
| - num_dev_ep               | 7               | 6            | 6             | 15           | 3                         | 5                | 5                | 5                 | 8            | 8            | 8              | 5            | 8            | 6            | 0           |
| - num_host_ch              | 7               | 13           | 7             | 15           | 7                         | 11               | 11               | 11                | 15           | 15           | 15             | 11           | 15           | 13           | 0           |
| - period_channel_support   | 1               | 1            | 1             | 1            | 1                         | 1                | 1                | 1                 | 1            | 1            | 1              | 1            | 1            | 1            | 0           |
| - enable_dynamic_fifo      | 1               | 1            | 1             | 1            | 1                         | 1                | 1                | 1                 | 1            | 1            | 1              | 1            | 1            | 1            | 0           |
| - mul_proc_intrpt          | 0               | 0            | 0             | 1            | 1                         | 1                | 1                | 1                 | 1            | 1            | 1              | 1            | 0            | 0            | 0           |
| - reserved21               | 0               | 0            | 0             | 0            | 0                         | 0                | 0                | 0                 | 0            | 0            | 0              | 0            | 0            | 0            | 0           |
| - nptx_q_depth             | 8               | 8            | 4             | 4            | 8                         | 8                | 8                | 8                 | 8            | 8            | 8              | 8            | 8            | 8            | 2           |
| - ptx_q_depth              | 8               | 8            | 8             | 4            | 8                         | 8                | 8                | 8                 | 8            | 8            | 8              | 8            | 8            | 8            | 2           |
| - token_q_depth            | 8               | 8            | 8             | 8            | 8                         | 8                | 8                | 8                 | 8            | 8            | 8              | 8            | 8            | 8            | 0           |
| - otg_enable_ic_usb        | 0               | 0            | 0             | 0            | 0                         | 0                | 0                | 0                 | 0            | 0            | 0              | 0            | 0            | 0            | 0           |
| GHWCFG3                    | 0x0FF000E8      | 0x01F204E8   | 0x00C804B5    | 0x03805EB5   | 0x020001E8                | 0x03F403E8       | 0x0200D1E8       | 0x0200D1E8        | 0x03EED2E8   | 0x03EED2E8   | 0x03B8D2E8     | 0x0200D1E8   | 0x03B882E8   | 0x027A01E5   | 0x00000000  |
| - xfer_size_width          | 8               | 8            | 5             | 5            | 8                         | 8                | 8                | 8                 | 8            | 8            | 8              | 8            | 8            | 5            | 0           |
| - packet_size_width        | 6               | 6            | 3             | 3            | 6                         | 6                | 6                | 6                 | 6            | 6            | 6              | 6            | 6            | 6            | 0           |
| - otg_enable               | 1               | 1            | 1             | 1            | 1                         | 1                | 1                | 1                 | 1            | 1            | 1              | 1            | 1            | 1            | 0           |
| - i2c_enable               | 0               | 0            | 0             | 0            | 1                         | 1                | 1                | 1                 | 0            | 0            | 0              | 1            | 0            | 1            | 0           |
| - vendor_ctrl_itf          | 0               | 0            | 0             | 1            | 0                         | 1                | 0                | 0                 | 1            | 1            | 1              | 0            | 1            | 0            | 0           |
| - optional_feature_removed | 0               | 1            | 1             | 1            | 0                         | 0                | 0                | 0                 | 0            | 0            | 0              | 0            | 0            | 0            | 0           |
| - synch_reset              | 0               | 0            | 0             | 1            | 0                         | 0                | 0                | 0                 | 0            | 0            | 0              | 0            | 0            | 0            | 0           |
| - otg_adp_support          | 0               | 0            | 0             | 1            | 0                         | 0                | 1                | 1                 | 1            | 1            | 1              | 1            | 0            | 0            | 0           |
| - otg_enable_hsic          | 0               | 0            | 0             | 0            | 0                         | 0                | 0                | 0                 | 0            | 0            | 0              | 0            | 0            | 0            | 0           |
| - battery_charger_support  | 0               | 0            | 0             | 1            | 0                         | 0                | 1                | 1                 | 1            | 1            | 1              | 1            | 0            | 0            | 0           |
| - lpm_mode                 | 0               | 0            | 0             | 0            | 0                         | 0                | 1                | 1                 | 1            | 1            | 1              | 1            | 1            | 0            | 0           |
| - dfifo_depth              | 4080            | 498          | 200           | 896          | 512                       | 1012             | 512              | 512               | 1006         | 1006         | 952            | 512          | 952          | 634          | 0           |
| GHWCFG4                    | 0x1FF00020      | 0x1BF08030   | 0xD3F0A030    | 0xDFF1A030   | 0x0FF08030                | 0x17F00030       | 0x17F08030       | 0x17F08030        | 0x23F00030   | 0x23F00030   | 0xE3F00030     | 0x17F08030   | 0xE2103E30   | 0xDBF08030   | 0x00000000  |
| - num_dev_period_in_ep     | 0               | 0            | 0             | 0            | 0                         | 0                | 0                | 0                 | 0            | 0            | 0              | 0            | 0            | 0            | 0           |
| - partial_powerdown        | 0               | 1            | 1             | 1            | 1                         | 1                | 1                | 1                 | 1            | 1            | 1              | 1            | 1            | 1            | 0           |
| - ahb_freq_min             | 1               | 1            | 1             | 1            | 1                         | 1                | 1                | 1                 | 1            | 1            | 1              | 1            | 1            | 1            | 0           |
| - hibernation              | 0               | 0            | 0             | 0            | 0                         | 0                | 0                | 0                 | 0            | 0            | 0              | 0            | 0            | 0            | 0           |
| - extended_hibernation     | 0               | 0            | 0             | 0            | 0                         | 0                | 0                | 0                 | 0            | 0            | 0              | 0            | 0            | 0            | 0           |
| - reserved8                | 0               | 0            | 0             | 0            | 0                         | 0                | 0                | 0                 | 0            | 0            | 0              | 0            | 0            | 0            | 0           |
| - enhanced_lpm_support1    | 0               | 0            | 0             | 0            | 0                         | 0                | 0                | 0                 | 0            | 0            | 0              | 0            | 1            | 0            | 0           |
| - service_interval_flow    | 0               | 0            | 0             | 0            | 0                         | 0                | 0                | 0                 | 0            | 0            | 0              | 0            | 1            | 0            | 0           |
| - ipg_isoc_support         | 0               | 0            | 0             | 0            | 0                         | 0                | 0                | 0                 | 0            | 0            | 0              | 0            | 1            | 0            | 0           |
| - acg_support              | 0               | 0            | 0             | 0            | 0                         | 0                | 0                | 0                 | 0            | 0            | 0              | 0            | 1            | 0            | 0           |
| - enhanced_lpm_support     | 0               | 0            | 1             | 1            | 0                         | 0                | 0                | 0                 | 0            | 0            | 0              | 0            | 1            | 0            | 0           |
| - phy_data_width           | 8 bit           | 8/16 bit     | 8/16 bit      | 8/16 bit     | 8/16 bit                  | 8 bit            | 8/16 bit         | 8/16 bit          | 8 bit        | 8 bit        | 8 bit          | 8/16 bit     | 8 bit        | 8/16 bit     | 8 bit       |
| - ctrl_ep_num              | 0               | 0            | 0             | 1            | 0                         | 0                | 0                | 0                 | 0            | 0            | 0              | 0            | 0            | 0            | 0           |
| - iddg_filter              | 1               | 1            | 1             | 1            | 1                         | 1                | 1                | 1                 | 1            | 1            | 1              | 1            | 1            | 1            | 0           |
| - vbus_valid_filter        | 1               | 1            | 1             | 1            | 1                         | 1                | 1                | 1                 | 1            | 1            | 1              | 1            | 0            | 1            | 0           |
| - a_valid_filter           | 1               | 1            | 1             | 1            | 1                         | 1                | 1                | 1                 | 1            | 1            | 1              | 1            | 0            | 1            | 0           |
| - b_valid_filter           | 1               | 1            | 1             | 1            | 1                         | 1                | 1                | 1                 | 1            | 1            | 1              | 1            | 0            | 1            | 0           |
| - session_end_filter       | 1               | 1            | 1             | 1            | 1                         | 1                | 1                | 1                 | 1            | 1            | 1              | 1            | 0            | 1            | 0           |
| - dedicated_fifos          | 1               | 1            | 1             | 1            | 1                         | 1                | 1                | 1                 | 1            | 1            | 1              | 1            | 1            | 1            | 0           |
| - num_dev_in_eps           | 7               | 6            | 4             | 7            | 3                         | 5                | 5                | 5                 | 8            | 8            | 8              | 5            | 8            | 6            | 0           |
| - dma_desc_enable          | 0               | 0            | 1             | 1            | 0                         | 0                | 0                | 0                 | 0            | 0            | 1              | 0            | 1            | 1            | 0           |
| - dma_desc_dynamic         | 0               | 0            | 1             | 1            | 0                         | 0                | 0                | 0                 | 0            | 0            | 1              | 0            | 1            | 1            | 0           |
