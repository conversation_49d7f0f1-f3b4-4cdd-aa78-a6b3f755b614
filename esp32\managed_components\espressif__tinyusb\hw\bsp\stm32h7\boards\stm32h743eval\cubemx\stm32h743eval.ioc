#MicroXplorer Configuration settings - do not modify
CAD.formats=[]
CAD.pinconfig=Project naming
CAD.provider=
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C1.IPParameters=Timing
I2C1.Timing=0x10C0ECFF
KeepUserPlacement=false
Mcu.CPN=STM32H743XIH6
Mcu.Family=STM32H7
Mcu.IP0=CORTEX_M7
Mcu.IP1=DEBUG
Mcu.IP2=I2C1
Mcu.IP3=NVIC
Mcu.IP4=RCC
Mcu.IP5=SYS
Mcu.IP6=USB_OTG_FS
Mcu.IP7=USB_OTG_HS
Mcu.IPNb=8
Mcu.Name=STM32H743XIHx
Mcu.Package=TFBGA240
Mcu.Pin0=PI6
Mcu.Pin1=PI5
Mcu.Pin10=PA15 (JTDI)
Mcu.Pin100=PC1
Mcu.Pin101=PC2
Mcu.Pin102=PC3
Mcu.Pin103=PJ9
Mcu.Pin104=PH2
Mcu.Pin105=PA2
Mcu.Pin106=PA1
Mcu.Pin107=PJ0
Mcu.Pin108=PE10
Mcu.Pin109=PJ8
Mcu.Pin11=PI1
Mcu.Pin110=PJ7
Mcu.Pin111=PJ6
Mcu.Pin112=PH3
Mcu.Pin113=PH4
Mcu.Pin114=PH5
Mcu.Pin115=PI15
Mcu.Pin116=PJ1
Mcu.Pin117=PF13
Mcu.Pin118=PF14
Mcu.Pin119=PE9
Mcu.Pin12=PI0
Mcu.Pin120=PE11
Mcu.Pin121=PB10
Mcu.Pin122=PB11
Mcu.Pin123=PH10
Mcu.Pin124=PH11
Mcu.Pin125=PD15
Mcu.Pin126=PD14
Mcu.Pin127=PA6
Mcu.Pin128=PA7
Mcu.Pin129=PB2
Mcu.Pin13=PI7
Mcu.Pin130=PF12
Mcu.Pin131=PF15
Mcu.Pin132=PE12
Mcu.Pin133=PE15
Mcu.Pin134=PJ5
Mcu.Pin135=PH9
Mcu.Pin136=PH12
Mcu.Pin137=PD11
Mcu.Pin138=PD12
Mcu.Pin139=PD13
Mcu.Pin14=PE1
Mcu.Pin140=PA0_C
Mcu.Pin141=PA5
Mcu.Pin142=PC4
Mcu.Pin143=PB1
Mcu.Pin144=PJ2
Mcu.Pin145=PF11
Mcu.Pin146=PG0
Mcu.Pin147=PE8
Mcu.Pin148=PE13
Mcu.Pin149=PH6
Mcu.Pin15=PB6
Mcu.Pin150=PH8
Mcu.Pin151=PB12
Mcu.Pin152=PB15
Mcu.Pin153=PD10
Mcu.Pin154=PD9
Mcu.Pin155=PA3
Mcu.Pin156=PA4
Mcu.Pin157=PC5
Mcu.Pin158=PB0
Mcu.Pin159=PJ3
Mcu.Pin16=PB4 (NJTRST)
Mcu.Pin160=PJ4
Mcu.Pin161=PG1
Mcu.Pin162=PE7
Mcu.Pin163=PE14
Mcu.Pin164=PH7
Mcu.Pin165=PB13
Mcu.Pin166=PB14
Mcu.Pin167=PD8
Mcu.Pin168=VP_SYS_VS_Systick
Mcu.Pin17=PK4
Mcu.Pin18=PG11
Mcu.Pin19=PJ15
Mcu.Pin2=PI4
Mcu.Pin20=PD6
Mcu.Pin21=PD3
Mcu.Pin22=PC11
Mcu.Pin23=PA14 (JTCK/SWCLK)
Mcu.Pin24=PI2
Mcu.Pin25=PH15
Mcu.Pin26=PH14
Mcu.Pin27=PC15-OSC32_OUT (OSC32_OUT)
Mcu.Pin28=PC14-OSC32_IN (OSC32_IN)
Mcu.Pin29=PE2
Mcu.Pin3=PB5
Mcu.Pin30=PE0
Mcu.Pin31=PB7
Mcu.Pin32=PB3 (JTDO/TRACESWO)
Mcu.Pin33=PK6
Mcu.Pin34=PK3
Mcu.Pin35=PG12
Mcu.Pin36=PD7
Mcu.Pin37=PC12
Mcu.Pin38=PI3
Mcu.Pin39=PA13 (JTMS/SWDIO)
Mcu.Pin4=PK5
Mcu.Pin40=PE5
Mcu.Pin41=PE4
Mcu.Pin42=PE3
Mcu.Pin43=PB9
Mcu.Pin44=PB8
Mcu.Pin45=PG15
Mcu.Pin46=PK7
Mcu.Pin47=PG14
Mcu.Pin48=PG13
Mcu.Pin49=PJ14
Mcu.Pin5=PG10
Mcu.Pin50=PJ12
Mcu.Pin51=PD2
Mcu.Pin52=PD0
Mcu.Pin53=PA10
Mcu.Pin54=PA9
Mcu.Pin55=PH13
Mcu.Pin56=PI9
Mcu.Pin57=PC13
Mcu.Pin58=PI8
Mcu.Pin59=PE6
Mcu.Pin6=PG9
Mcu.Pin60=PJ13
Mcu.Pin61=PD1
Mcu.Pin62=PC8
Mcu.Pin63=PC9
Mcu.Pin64=PA8
Mcu.Pin65=PA12
Mcu.Pin66=PA11
Mcu.Pin67=PI10
Mcu.Pin68=PI11
Mcu.Pin69=PC7
Mcu.Pin7=PD5
Mcu.Pin70=PC6
Mcu.Pin71=PG8
Mcu.Pin72=PG7
Mcu.Pin73=PF2
Mcu.Pin74=PF1
Mcu.Pin75=PF0
Mcu.Pin76=PG5
Mcu.Pin77=PG6
Mcu.Pin78=PI12
Mcu.Pin79=PI13
Mcu.Pin8=PD4
Mcu.Pin80=PI14
Mcu.Pin81=PF3
Mcu.Pin82=PG4
Mcu.Pin83=PG3
Mcu.Pin84=PG2
Mcu.Pin85=PK2
Mcu.Pin86=PH1-OSC_OUT (PH1)
Mcu.Pin87=PH0-OSC_IN (PH0)
Mcu.Pin88=PF5
Mcu.Pin89=PF4
Mcu.Pin9=PC10
Mcu.Pin90=PK0
Mcu.Pin91=PK1
Mcu.Pin92=PF6
Mcu.Pin93=PF7
Mcu.Pin94=PF8
Mcu.Pin95=PJ11
Mcu.Pin96=PC0
Mcu.Pin97=PF10
Mcu.Pin98=PF9
Mcu.Pin99=PJ10
Mcu.PinsNb=169
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32H743XIHx
MxCube.Version=6.10.0
MxDb.Version=DB.6.0.100
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:true\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:true\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:true\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:true\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:true\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:true\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:false
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:true\:false\:false
PA0_C.GPIOParameters=GPIO_Label
PA0_C.GPIO_Label=Potentiometer
PA0_C.Locked=true
PA0_C.Signal=ADCx_INN1
PA1.GPIOParameters=GPIO_Label
PA1.GPIO_Label=RMII_REF_CLK [LAN8742A_REFCLK0]
PA1.Locked=true
PA1.Signal=ETH_REF_CLK
PA10.GPIOParameters=GPIO_Label
PA10.GPIO_Label=USB_FS1_ID
PA10.Locked=true
PA10.Signal=USB_OTG_FS_ID
PA11.GPIOParameters=GPIO_Label
PA11.GPIO_Label=USB_FS1_DM
PA11.Locked=true
PA11.Mode=Device_Only
PA11.Signal=USB_OTG_FS_DM
PA12.GPIOParameters=GPIO_Label
PA12.GPIO_Label=USB_FS1_DP
PA12.Locked=true
PA12.Mode=Device_Only
PA12.Signal=USB_OTG_FS_DP
PA13\ (JTMS/SWDIO).Locked=true
PA13\ (JTMS/SWDIO).Mode=Trace_Synchro_4bits_SW
PA13\ (JTMS/SWDIO).Signal=DEBUG_JTMS-SWDIO
PA14\ (JTCK/SWCLK).Locked=true
PA14\ (JTCK/SWCLK).Mode=Trace_Synchro_4bits_SW
PA14\ (JTCK/SWCLK).Signal=DEBUG_JTCK-SWCLK
PA15\ (JTDI).GPIOParameters=GPIO_Label
PA15\ (JTDI).GPIO_Label=TDI
PA15\ (JTDI).Locked=true
PA15\ (JTDI).Signal=DEBUG_JTDI
PA2.GPIOParameters=GPIO_Label
PA2.GPIO_Label=ETH_MDIO [LAN8742A_MDIO]
PA2.Locked=true
PA2.Signal=ETH_MDIO
PA3.GPIOParameters=GPIO_Label
PA3.GPIO_Label=ULPI_D0 [USB3320C_D0]
PA3.Locked=true
PA3.Mode=Device_HS
PA3.Signal=USB_OTG_HS_ULPI_D0
PA4.GPIOParameters=GPIO_Label
PA4.GPIO_Label=LED3_RGB [LD3_Red]
PA4.Locked=true
PA4.Signal=GPIO_Output
PA5.GPIOParameters=GPIO_Label
PA5.GPIO_Label=ULPI_CK  [USB3320C_CLKOUT]
PA5.Locked=true
PA5.Mode=Device_HS
PA5.Signal=USB_OTG_HS_ULPI_CK
PA6.GPIOParameters=GPIO_Label
PA6.GPIO_Label=LCD_BL_CTRL
PA6.Locked=true
PA6.Signal=GPIO_Output
PA7.GPIOParameters=GPIO_Label
PA7.GPIO_Label=RMII_CRS_DV [LAN8742A_CRS_DV]
PA7.Locked=true
PA7.Signal=ETH_CRS_DV
PA8.GPIOParameters=GPIO_Label
PA8.GPIO_Label=MCO
PA8.Locked=true
PA8.Signal=RCC_MCO_1
PA9.GPIOParameters=GPIO_Label
PA9.GPIO_Label=VBUS_FS1
PA9.Locked=true
PA9.Signal=USB_OTG_FS_VBUS
PB0.GPIOParameters=GPIO_Label
PB0.GPIO_Label=ULPI_D1 [USB3320C_D1]
PB0.Locked=true
PB0.Mode=Device_HS
PB0.Signal=USB_OTG_HS_ULPI_D1
PB1.GPIOParameters=GPIO_Label
PB1.GPIO_Label=ULPI_D2 [USB3320C_D2]
PB1.Locked=true
PB1.Mode=Device_HS
PB1.Signal=USB_OTG_HS_ULPI_D2
PB10.GPIOParameters=GPIO_Label
PB10.GPIO_Label=ULPI_D3 [USB3320C_D3]
PB10.Locked=true
PB10.Mode=Device_HS
PB10.Signal=USB_OTG_HS_ULPI_D3
PB11.GPIOParameters=GPIO_Label
PB11.GPIO_Label=ULPI_D4 [USB3320C_D4]
PB11.Locked=true
PB11.Mode=Device_HS
PB11.Signal=USB_OTG_HS_ULPI_D4
PB12.GPIOParameters=GPIO_Label
PB12.GPIO_Label=ULPI_D5 [USB3320C_D5]
PB12.Locked=true
PB12.Mode=Device_HS
PB12.Signal=USB_OTG_HS_ULPI_D5
PB13.GPIOParameters=GPIO_Label
PB13.GPIO_Label=ULPI_D6 [USB3320C_D6]
PB13.Locked=true
PB13.Mode=Device_HS
PB13.Signal=USB_OTG_HS_ULPI_D6
PB14.GPIOParameters=GPIO_Label
PB14.GPIO_Label=RS232_TX [ST3241EBPR_T2IN]
PB14.Locked=true
PB14.Signal=USART1_TX
PB15.GPIOParameters=GPIO_Label
PB15.GPIO_Label=RS_232RX [ST3241EBPR_R3OUT]
PB15.Locked=true
PB15.Signal=USART1_RX
PB2.GPIOParameters=GPIO_Label
PB2.GPIO_Label=QSPI_CLK [MT25TL01GHBA8ESF_CLK_1]
PB2.Locked=true
PB2.Signal=QUADSPI_CLK
PB3\ (JTDO/TRACESWO).Locked=true
PB3\ (JTDO/TRACESWO).Signal=DEBUG_JTDO-SWO
PB4\ (NJTRST).GPIOParameters=GPIO_Label
PB4\ (NJTRST).GPIO_Label=TRST
PB4\ (NJTRST).Locked=true
PB4\ (NJTRST).Signal=DEBUG_JTRST
PB5.GPIOParameters=GPIO_Label
PB5.GPIO_Label=ULPI_D7 [USB3320C_D7]
PB5.Locked=true
PB5.Mode=Device_HS
PB5.Signal=USB_OTG_HS_ULPI_D7
PB6.GPIOParameters=GPIO_Label
PB6.GPIO_Label=I2C1_SCL [STM32L152CCT6_I2C_SCL]
PB6.Locked=true
PB6.Mode=I2C
PB6.Signal=I2C1_SCL
PB7.GPIOParameters=GPIO_Label
PB7.GPIO_Label=I2C1_SDA [STM32L152CCT6_I2C_SDA]
PB7.Locked=true
PB7.Mode=I2C
PB7.Signal=I2C1_SDA
PB8.GPIOParameters=GPIO_Label
PB8.GPIO_Label=SDIO1_CKIN
PB8.Locked=true
PB8.Signal=SDMMC1_CKIN
PB9.GPIOParameters=GPIO_Label
PB9.GPIO_Label=SDIO1_CDIR
PB9.Locked=true
PB9.Signal=SDMMC1_CDIR
PC0.GPIOParameters=GPIO_Label
PC0.GPIO_Label=ULPI_STP [USB3320C_STP]
PC0.Locked=true
PC0.Mode=Device_HS
PC0.Signal=USB_OTG_HS_ULPI_STP
PC1.GPIOParameters=GPIO_Label
PC1.GPIO_Label=RMII_MDC [LAN8742A_MDC]
PC1.Locked=true
PC1.Signal=ETH_MDC
PC10.GPIOParameters=GPIO_Label
PC10.GPIO_Label=SDIO1_D2
PC10.Locked=true
PC10.Signal=SDMMC1_D2
PC11.GPIOParameters=GPIO_Label
PC11.GPIO_Label=SDIO1_D3
PC11.Locked=true
PC11.Signal=SDMMC1_D3
PC12.GPIOParameters=GPIO_Label
PC12.GPIO_Label=SDIO1_CLK
PC12.Locked=true
PC12.Signal=SDMMC1_CK
PC13.GPIOParameters=GPIO_Label
PC13.GPIO_Label=TAMPER_KEY [B1]
PC13.Locked=true
PC13.Signal=RTC_TAMP1
PC14-OSC32_IN\ (OSC32_IN).Locked=true
PC14-OSC32_IN\ (OSC32_IN).Signal=RCC_OSC32_IN
PC15-OSC32_OUT\ (OSC32_OUT).Locked=true
PC15-OSC32_OUT\ (OSC32_OUT).Signal=RCC_OSC32_OUT
PC2.GPIOParameters=GPIO_Label
PC2.GPIO_Label=DFSDM_CLK
PC2.Locked=true
PC2.Signal=S_CKOUTDFSDM1
PC3.GPIOParameters=GPIO_Label
PC3.GPIO_Label=DFSM_DAT1
PC3.Locked=true
PC3.Signal=S_DATAIN1DFSDM1
PC4.GPIOParameters=GPIO_Label
PC4.GPIO_Label=RMII_RXD0 [LAN8742A_RXD0]
PC4.Locked=true
PC4.Signal=ETH_RXD0
PC5.GPIOParameters=GPIO_Label
PC5.GPIO_Label=RMII_RXD1 [LAN8742A_RXD1]
PC5.Locked=true
PC5.Signal=ETH_RXD1
PC6.GPIOParameters=GPIO_Label
PC6.GPIO_Label=SDIO1_D0DIR
PC6.Locked=true
PC6.Signal=SDMMC1_D0DIR
PC7.Locked=true
PC7.Signal=DEBUG_TRGIO
PC8.GPIOParameters=GPIO_Label
PC8.GPIO_Label=SDIO1_D0
PC8.Locked=true
PC8.Signal=SDMMC1_D0
PC9.GPIOParameters=GPIO_Label
PC9.GPIO_Label=SDIO1_D1
PC9.Locked=true
PC9.Signal=SDMMC1_D1
PD0.GPIOParameters=GPIO_Label
PD0.GPIO_Label=D2 [IS42S32800G_DQ2]
PD0.Locked=true
PD0.Signal=FMC_D2_DA2
PD1.GPIOParameters=GPIO_Label
PD1.GPIO_Label=D3 [IS42S32800G_DQ3]
PD1.Locked=true
PD1.Signal=FMC_D3_DA3
PD10.GPIOParameters=GPIO_Label
PD10.GPIO_Label=D15 [IS42S32800G_DQ15]
PD10.Locked=true
PD10.Signal=FMC_D15_DA15
PD11.GPIOParameters=GPIO_Label
PD11.GPIO_Label=A16 [PC28F128M29EWLA_A16]
PD11.Locked=true
PD11.Signal=FMC_A16_CLE
PD12.GPIOParameters=GPIO_Label
PD12.GPIO_Label=A17 [PC28F128M29EWLA_A17]
PD12.Locked=true
PD12.Signal=FMC_A17_ALE
PD13.GPIOParameters=GPIO_Label
PD13.GPIO_Label=A18 [PC28F128M29EWLA_A18]
PD13.Locked=true
PD13.Signal=FMC_A18
PD14.GPIOParameters=GPIO_Label
PD14.GPIO_Label=D0 [IS42S32800G_DQ0]
PD14.Locked=true
PD14.Signal=FMC_D0_DA0
PD15.GPIOParameters=GPIO_Label
PD15.GPIO_Label=D1 [IS42S32800G_DQ1]
PD15.Locked=true
PD15.Signal=FMC_D1_DA1
PD2.GPIOParameters=GPIO_Label
PD2.GPIO_Label=SDIO1_CMD
PD2.Locked=true
PD2.Signal=SDMMC1_CMD
PD3.GPIOParameters=GPIO_Label
PD3.GPIO_Label=FDCAN1_STBY [MCP2562FD_STBY]
PD3.Locked=true
PD3.Signal=GPIO_Output
PD4.GPIOParameters=GPIO_Label
PD4.GPIO_Label=FMC_NOE [IS61WV102416BLL_OE]
PD4.Locked=true
PD4.Signal=FMC_NOE
PD5.GPIOParameters=GPIO_Label
PD5.GPIO_Label=FMC_NWE [IS61WV102416BLL_WE]
PD5.Locked=true
PD5.Signal=FMC_NWE
PD6.GPIOParameters=GPIO_Label
PD6.GPIO_Label=FMC_NWAIT [PC28F128M29EWLA_RB]
PD6.Locked=true
PD6.Signal=FMC_NWAIT
PD7.GPIOParameters=GPIO_Label
PD7.GPIO_Label=FMC_NE1 [PC28F128M29EWLA_E]
PD7.Locked=true
PD7.Signal=FMC_NE1
PD8.GPIOParameters=GPIO_Label
PD8.GPIO_Label=D13 [IS42S32800G_DQ13]
PD8.Locked=true
PD8.Signal=FMC_D13_DA13
PD9.GPIOParameters=GPIO_Label
PD9.GPIO_Label=D14 [IS42S32800G_DQ14]
PD9.Locked=true
PD9.Signal=FMC_D14_DA14
PE0.GPIOParameters=GPIO_Label
PE0.GPIO_Label=FMC_NBL0 [IS42S32800G_DQM0]
PE0.Locked=true
PE0.Signal=FMC_NBL0
PE1.GPIOParameters=GPIO_Label
PE1.GPIO_Label=FMC_NBL1 [IS42S32800G_DQM1]
PE1.Locked=true
PE1.Signal=FMC_NBL1
PE10.GPIOParameters=GPIO_Label
PE10.GPIO_Label=D7 [IS42S32800G_DQ7]
PE10.Locked=true
PE10.Signal=FMC_D7_DA7
PE11.GPIOParameters=GPIO_Label
PE11.GPIO_Label=D8 [IS42S32800G_DQ8]
PE11.Locked=true
PE11.Signal=FMC_D8_DA8
PE12.GPIOParameters=GPIO_Label
PE12.GPIO_Label=D9 [IS42S32800G_DQ9]
PE12.Locked=true
PE12.Signal=FMC_D9_DA9
PE13.GPIOParameters=GPIO_Label
PE13.GPIO_Label=D10 [IS42S32800G_DQ10]
PE13.Locked=true
PE13.Signal=FMC_D10_DA10
PE14.GPIOParameters=GPIO_Label
PE14.GPIO_Label=D11 [IS42S32800G_DQ11]
PE14.Locked=true
PE14.Signal=FMC_D11_DA11
PE15.GPIOParameters=GPIO_Label
PE15.GPIO_Label=D12 [IS42S32800G_DQ12]
PE15.Locked=true
PE15.Signal=FMC_D12_DA12
PE2.Locked=true
PE2.Mode=Trace_Synchro_4bits_SW
PE2.Signal=DEBUG_TRACECLK
PE3.Locked=true
PE3.Mode=Trace_Synchro_4bits_SW
PE3.Signal=DEBUG_TRACED0
PE4.Locked=true
PE4.Mode=Trace_Synchro_4bits_SW
PE4.Signal=DEBUG_TRACED1
PE5.Locked=true
PE5.Mode=Trace_Synchro_4bits_SW
PE5.Signal=DEBUG_TRACED2
PE6.Locked=true
PE6.Mode=Trace_Synchro_4bits_SW
PE6.Signal=DEBUG_TRACED3
PE7.GPIOParameters=GPIO_Label
PE7.GPIO_Label=D4 [IS42S32800G_DQ4]
PE7.Locked=true
PE7.Signal=FMC_D4_DA4
PE8.GPIOParameters=GPIO_Label
PE8.GPIO_Label=D5 [IS42S32800G_DQ5]
PE8.Locked=true
PE8.Signal=FMC_D5_DA5
PE9.GPIOParameters=GPIO_Label
PE9.GPIO_Label=D6 [IS42S32800G_DQ6]
PE9.Locked=true
PE9.Signal=FMC_D6_DA6
PF0.GPIOParameters=GPIO_Label
PF0.GPIO_Label=A0 [PC28F128M29EWLA_A0]
PF0.Locked=true
PF0.Signal=FMC_A0
PF1.GPIOParameters=GPIO_Label
PF1.GPIO_Label=A1 [PC28F128M29EWLA_A1]
PF1.Locked=true
PF1.Signal=FMC_A1
PF10.GPIOParameters=GPIO_Label
PF10.GPIO_Label=LED1_RGB [LD1_Green]
PF10.Locked=true
PF10.Signal=GPIO_Output
PF11.GPIOParameters=GPIO_Label
PF11.GPIO_Label=SNDRAS [IS42S32800G_RAS]
PF11.Locked=true
PF11.Signal=FMC_SDNRAS
PF12.GPIOParameters=GPIO_Label
PF12.GPIO_Label=A6 [PC28F128M29EWLA_A6]
PF12.Locked=true
PF12.Signal=FMC_A6
PF13.GPIOParameters=GPIO_Label
PF13.GPIO_Label=A7 [PC28F128M29EWLA_A7]
PF13.Locked=true
PF13.Signal=FMC_A7
PF14.GPIOParameters=GPIO_Label
PF14.GPIO_Label=A8 [PC28F128M29EWLA_A8]
PF14.Locked=true
PF14.Signal=FMC_A8
PF15.GPIOParameters=GPIO_Label
PF15.GPIO_Label=A9 [PC28F128M29EWLA_A9]
PF15.Locked=true
PF15.Signal=FMC_A9
PF2.GPIOParameters=GPIO_Label
PF2.GPIO_Label=A2 [PC28F128M29EWLA_A2]
PF2.Locked=true
PF2.Signal=FMC_A2
PF3.GPIOParameters=GPIO_Label
PF3.GPIO_Label=A3 [PC28F128M29EWLA_A3]
PF3.Locked=true
PF3.Signal=FMC_A3
PF4.GPIOParameters=GPIO_Label
PF4.GPIO_Label=A4 [PC28F128M29EWLA_A4]
PF4.Locked=true
PF4.Signal=FMC_A4
PF5.GPIOParameters=GPIO_Label
PF5.GPIO_Label=A5 [PC28F128M29EWLA_A5]
PF5.Locked=true
PF5.Signal=FMC_A5
PF6.GPIOParameters=GPIO_Label
PF6.GPIO_Label=QSPI_BK1_IO3 [MT25TL01GHBA8ESF_DQ3]
PF6.Locked=true
PF6.Signal=QUADSPI_BK1_IO3
PF7.GPIOParameters=GPIO_Label
PF7.GPIO_Label=QSPI_BK1_IO2 [MT25TL01GHBA8ESF_DQ2]
PF7.Locked=true
PF7.Signal=QUADSPI_BK1_IO2
PF8.GPIOParameters=GPIO_Label
PF8.GPIO_Label=QSPI_BK1_IO0 [MT25TL01GHBA8ESF_DQ0]
PF8.Locked=true
PF8.Signal=QUADSPI_BK1_IO0
PF9.GPIOParameters=GPIO_Label
PF9.GPIO_Label=QSPI_BK1_IO1 [MT25TL01GHBA8ESF_DQ1]
PF9.Locked=true
PF9.Signal=QUADSPI_BK1_IO1
PG0.GPIOParameters=GPIO_Label
PG0.GPIO_Label=A10 [PC28F128M29EWLA_A10]
PG0.Locked=true
PG0.Signal=FMC_A10
PG1.GPIOParameters=GPIO_Label
PG1.GPIO_Label=A11 [PC28F128M29EWLA_A11]
PG1.Locked=true
PG1.Signal=FMC_A11
PG10.GPIOParameters=GPIO_Label
PG10.GPIO_Label=FMC_NE3 [IS61WV102416BLL_CE]
PG10.Locked=true
PG10.Signal=FMC_NE3
PG11.GPIOParameters=GPIO_Label
PG11.GPIO_Label=RMII_TX_EN [LAN8742A_TXEN]
PG11.Locked=true
PG11.Signal=ETH_TX_EN
PG12.GPIOParameters=GPIO_Label
PG12.GPIO_Label=RMII_TXD1 [LAN8742A_TXD1]
PG12.Locked=true
PG12.Signal=ETH_TXD1
PG13.GPIOParameters=GPIO_Label
PG13.GPIO_Label=RMII_TXD0 [LAN8742A_TXD0]
PG13.Locked=true
PG13.Signal=ETH_TXD0
PG14.GPIOParameters=GPIO_Label
PG14.GPIO_Label=QSPI_BK2_IO3 [MT25TL01GHBA8ESF_DQ7]
PG14.Locked=true
PG14.Signal=QUADSPI_BK2_IO3
PG15.GPIOParameters=GPIO_Label
PG15.GPIO_Label=SDNCAS [IS42S32800G_CAS]
PG15.Locked=true
PG15.Signal=FMC_SDNCAS
PG2.GPIOParameters=GPIO_Label
PG2.GPIO_Label=A12 [PC28F128M29EWLA_A12]
PG2.Locked=true
PG2.Signal=FMC_A12
PG3.GPIOParameters=GPIO_Label
PG3.GPIO_Label=A13 [PC28F128M29EWLA_A13]
PG3.Locked=true
PG3.Signal=FMC_A13
PG4.Locked=true
PG4.Signal=FMC_A14_BA0
PG5.Locked=true
PG5.Signal=FMC_A15_BA1
PG6.GPIOParameters=GPIO_Label
PG6.GPIO_Label=QSPI_BK1_NCS [MT25TL01GHBA8ESF_CS]
PG6.Locked=true
PG6.Signal=QUADSPI_BK1_NCS
PG7.GPIOParameters=GPIO_Label
PG7.GPIO_Label=SAI1_MCLKA [WM8994ECS_MCLK1]
PG7.Locked=true
PG7.Signal=SAI1_MCLK_A
PG8.GPIOParameters=GPIO_Label
PG8.GPIO_Label=SDCLK [IS42S32800G_CLK]
PG8.Locked=true
PG8.Signal=FMC_SDCLK
PG9.GPIOParameters=GPIO_Label
PG9.GPIO_Label=QSPI_BK2_IO2 [MT25TL01GHBA8ESF_DQ6]
PG9.Locked=true
PG9.Signal=QUADSPI_BK2_IO2
PH0-OSC_IN\ (PH0).Locked=true
PH0-OSC_IN\ (PH0).Mode=HSE-External-Oscillator
PH0-OSC_IN\ (PH0).Signal=RCC_OSC_IN
PH1-OSC_OUT\ (PH1).Locked=true
PH1-OSC_OUT\ (PH1).Mode=HSE-External-Oscillator
PH1-OSC_OUT\ (PH1).Signal=RCC_OSC_OUT
PH10.GPIOParameters=GPIO_Label
PH10.GPIO_Label=D18 [IS42S32800G_DQ18]
PH10.Locked=true
PH10.Signal=FMC_D18
PH11.GPIOParameters=GPIO_Label
PH11.GPIO_Label=D19 [IS42S32800G_DQ19]
PH11.Locked=true
PH11.Signal=FMC_D19
PH12.GPIOParameters=GPIO_Label
PH12.GPIO_Label=D20 [IS42S32800G_DQ20]
PH12.Locked=true
PH12.Signal=FMC_D20
PH13.GPIOParameters=GPIO_Label
PH13.GPIO_Label=D21 [IS42S32800G_DQ21]
PH13.Locked=true
PH13.Signal=FMC_D21
PH14.GPIOParameters=GPIO_Label
PH14.GPIO_Label=D22 [IS42S32800G_DQ22]
PH14.Locked=true
PH14.Signal=FMC_D22
PH15.GPIOParameters=GPIO_Label
PH15.GPIO_Label=D23 [IS42S32800G_DQ23]
PH15.Locked=true
PH15.Signal=FMC_D23
PH2.GPIOParameters=GPIO_Label
PH2.GPIO_Label=QSPI_BK2_IO0 [MT25TL01GHBA8ESF_DQ4]
PH2.Locked=true
PH2.Signal=QUADSPI_BK2_IO0
PH3.GPIOParameters=GPIO_Label
PH3.GPIO_Label=QSPI_BK2_IO1 [MT25TL01GHBA8ESF_DQ5]
PH3.Locked=true
PH3.Signal=QUADSPI_BK2_IO1
PH4.GPIOParameters=GPIO_Label
PH4.GPIO_Label=ULPI_NXT [USB3320C_NXT]
PH4.Locked=true
PH4.Mode=Device_HS
PH4.Signal=USB_OTG_HS_ULPI_NXT
PH5.GPIOParameters=GPIO_Label
PH5.GPIO_Label=SDNWE [IS42S32800G_WE]
PH5.Locked=true
PH5.Signal=FMC_SDNWE
PH6.GPIOParameters=GPIO_Label
PH6.GPIO_Label=SDNE1 [IS42S32800G_CS]
PH6.Locked=true
PH6.Signal=FMC_SDNE1
PH7.GPIOParameters=GPIO_Label
PH7.GPIO_Label=SDCKE1 [IS42S32800G_CKE]
PH7.Locked=true
PH7.Signal=FMC_SDCKE1
PH8.GPIOParameters=GPIO_Label
PH8.GPIO_Label=D16 [IS42S32800G_DQ16]
PH8.Locked=true
PH8.Signal=FMC_D16
PH9.GPIOParameters=GPIO_Label
PH9.GPIO_Label=D17 [IS42S32800G_DQ17]
PH9.Locked=true
PH9.Signal=FMC_D17
PI0.GPIOParameters=GPIO_Label
PI0.GPIO_Label=D24 [IS42S32800G_DQ24]
PI0.Locked=true
PI0.Signal=FMC_D24
PI1.GPIOParameters=GPIO_Label
PI1.GPIO_Label=D25 [IS42S32800G_DQ25]
PI1.Locked=true
PI1.Signal=FMC_D25
PI10.GPIOParameters=GPIO_Label
PI10.GPIO_Label=D31 [IS42S32800G_DQ31]
PI10.Locked=true
PI10.Signal=FMC_D31
PI11.GPIOParameters=GPIO_Label
PI11.GPIO_Label=ULPI_DIR [USB3320C_DIR]
PI11.Locked=true
PI11.Mode=Device_HS
PI11.Signal=USB_OTG_HS_ULPI_DIR
PI12.GPIOParameters=GPIO_Label
PI12.GPIO_Label=LCD_HSYNC
PI12.Locked=true
PI12.Signal=LTDC_HSYNC
PI13.GPIOParameters=GPIO_Label
PI13.GPIO_Label=LCD_VSYNC
PI13.Locked=true
PI13.Signal=LTDC_VSYNC
PI14.GPIOParameters=GPIO_Label
PI14.GPIO_Label=LCD_CLK
PI14.Locked=true
PI14.Signal=LTDC_CLK
PI15.GPIOParameters=GPIO_Label
PI15.GPIO_Label=LCD_R0
PI15.Locked=true
PI15.Signal=LTDC_R0
PI2.GPIOParameters=GPIO_Label
PI2.GPIO_Label=D26 [IS42S32800G_DQ26]
PI2.Locked=true
PI2.Signal=FMC_D26
PI3.GPIOParameters=GPIO_Label
PI3.GPIO_Label=D27 [IS42S32800G_DQ27
PI3.Locked=true
PI3.Signal=FMC_D27
PI4.GPIOParameters=GPIO_Label
PI4.GPIO_Label=FMC_NBL2 [IS42S32800G_DQM2]
PI4.Locked=true
PI4.Signal=FMC_NBL2
PI5.GPIOParameters=GPIO_Label
PI5.GPIO_Label=FMC_NBL3 [IS42S32800G_DQM3]
PI5.Locked=true
PI5.Signal=FMC_NBL3
PI6.GPIOParameters=GPIO_Label
PI6.GPIO_Label=D28 [IS42S32800G_DQ28]
PI6.Locked=true
PI6.Signal=FMC_D28
PI7.GPIOParameters=GPIO_Label
PI7.GPIO_Label=D29 [IS42S32800G_DQ29]
PI7.Locked=true
PI7.Signal=FMC_D29
PI8.GPIOParameters=GPIO_Label
PI8.GPIO_Label=MFX_IRQOUT [MFX_V3_IRQOUT]
PI8.Locked=true
PI8.Signal=GPXTI8
PI9.GPIOParameters=GPIO_Label
PI9.GPIO_Label=D30 [IS42S32800G_DQ30]
PI9.Locked=true
PI9.Signal=FMC_D30
PJ0.GPIOParameters=GPIO_Label
PJ0.GPIO_Label=LCD_R1
PJ0.Locked=true
PJ0.Signal=LTDC_R1
PJ1.GPIOParameters=GPIO_Label
PJ1.GPIO_Label=LCD_R2
PJ1.Locked=true
PJ1.Signal=LTDC_R2
PJ10.GPIOParameters=GPIO_Label
PJ10.GPIO_Label=LCd_G3
PJ10.Locked=true
PJ10.Signal=LTDC_G3
PJ11.GPIOParameters=GPIO_Label
PJ11.GPIO_Label=LCD_G4
PJ11.Locked=true
PJ11.Signal=LTDC_G4
PJ12.Locked=true
PJ12.Signal=DEBUG_TRGOUT
PJ13.GPIOParameters=GPIO_Label
PJ13.GPIO_Label=LCD_B1
PJ13.Locked=true
PJ13.Signal=LTDC_B1
PJ14.GPIOParameters=GPIO_Label
PJ14.GPIO_Label=LCD_B2
PJ14.Locked=true
PJ14.Signal=LTDC_B2
PJ15.GPIOParameters=GPIO_Label
PJ15.GPIO_Label=LCD_B3
PJ15.Locked=true
PJ15.Signal=LTDC_B3
PJ2.GPIOParameters=GPIO_Label
PJ2.GPIO_Label=LCD_R3
PJ2.Locked=true
PJ2.Signal=LTDC_R3
PJ3.GPIOParameters=GPIO_Label
PJ3.GPIO_Label=LCD_R4
PJ3.Locked=true
PJ3.Signal=LTDC_R4
PJ4.GPIOParameters=GPIO_Label
PJ4.GPIO_Label=LCD_R5
PJ4.Locked=true
PJ4.Signal=LTDC_R5
PJ5.GPIOParameters=GPIO_Label
PJ5.GPIO_Label=LCD_R6
PJ5.Locked=true
PJ5.Signal=LTDC_R6
PJ6.GPIOParameters=GPIO_Label
PJ6.GPIO_Label=LCD_R7
PJ6.Locked=true
PJ6.Signal=LTDC_R7
PJ7.Locked=true
PJ7.Signal=DEBUG_TRGIN
PJ8.GPIOParameters=GPIO_Label
PJ8.GPIO_Label=LCD_G1
PJ8.Locked=true
PJ8.Signal=LTDC_G1
PJ9.GPIOParameters=GPIO_Label
PJ9.GPIO_Label=LCD_G2
PJ9.Locked=true
PJ9.Signal=LTDC_G2
PK0.GPIOParameters=GPIO_Label
PK0.GPIO_Label=LCD_G5
PK0.Locked=true
PK0.Signal=LTDC_G5
PK1.GPIOParameters=GPIO_Label
PK1.GPIO_Label=LCD_G6
PK1.Locked=true
PK1.Signal=LTDC_G6
PK2.GPIOParameters=GPIO_Label
PK2.GPIO_Label=LCD_G7
PK2.Locked=true
PK2.Signal=LTDC_G7
PK3.GPIOParameters=GPIO_Label
PK3.GPIO_Label=LCD_B4
PK3.Locked=true
PK3.Signal=LTDC_B4
PK4.GPIOParameters=GPIO_Label
PK4.GPIO_Label=LCD_B5
PK4.Locked=true
PK4.Signal=LTDC_B5
PK5.GPIOParameters=GPIO_Label
PK5.GPIO_Label=LCD_B6
PK5.Locked=true
PK5.Signal=LTDC_B6
PK6.GPIOParameters=GPIO_Label
PK6.GPIO_Label=LCD_B7
PK6.Locked=true
PK6.Signal=LTDC_B7
PK7.GPIOParameters=GPIO_Label
PK7.GPIO_Label=LCD_DE
PK7.Locked=true
PK7.Signal=LTDC_DE
PinOutPanel.CurrentBGAView=Top
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32H743XIHx
ProjectManager.FirmwarePackage=STM32Cube FW_H7 V1.11.0
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=false
ProjectManager.LibraryCopy=2
ProjectManager.MainLocation=Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=stm32h743eval.ioc
ProjectManager.ProjectName=stm32h743eval
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=Makefile
ProjectManager.ToolChainLocation=Src/
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_USB_OTG_HS_PCD_Init-USB_OTG_HS-false-HAL-true,4-MX_I2C1_Init-I2C1-false-HAL-true,5-MX_USB_OTG_FS_PCD_Init-USB_OTG_FS-false-HAL-true,0-MX_CORTEX_M7_Init-CORTEX_M7-false-HAL-true
RCC.ADCFreq_Value=50390625
RCC.AHB12Freq_Value=*********
RCC.AHB4Freq_Value=*********
RCC.APB1Freq_Value=*********
RCC.APB2Freq_Value=*********
RCC.APB3Freq_Value=*********
RCC.APB4Freq_Value=*********
RCC.AXIClockFreq_Value=*********
RCC.CECFreq_Value=32000
RCC.CKPERFreq_Value=64000000
RCC.CortexFreq_Value=*********
RCC.CpuClockFreq_Value=*********
RCC.D1CPREFreq_Value=*********
RCC.D1PPRE=RCC_APB3_DIV2
RCC.D2PPRE1=RCC_APB1_DIV2
RCC.D2PPRE2=RCC_APB2_DIV2
RCC.D3PPRE=RCC_APB4_DIV2
RCC.DFSDMACLkFreq_Value=*********
RCC.DFSDMFreq_Value=*********
RCC.DIVM1=5
RCC.DIVM3=25
RCC.DIVN1=160
RCC.DIVN3=336
RCC.DIVP1Freq_Value=*********
RCC.DIVP2Freq_Value=50390625
RCC.DIVP3Freq_Value=*********
RCC.DIVQ1=4
RCC.DIVQ1Freq_Value=*********
RCC.DIVQ2Freq_Value=50390625
RCC.DIVQ3=7
RCC.DIVQ3Freq_Value=48000000
RCC.DIVR1=6
RCC.DIVR1Freq_Value=*********.33333333
RCC.DIVR2Freq_Value=50390625
RCC.DIVR3Freq_Value=*********
RCC.EnbaleCSS=true
RCC.FDCANFreq_Value=*********
RCC.FMCFreq_Value=*********
RCC.FamilyName=M
RCC.HCLK3ClockFreq_Value=*********
RCC.HCLKFreq_Value=*********
RCC.HPRE=RCC_HCLK_DIV2
RCC.HPREFreq_Value=64000000
RCC.HRTIMFreq_Value=*********
RCC.HSICalibrationValue=32
RCC.I2C123Freq_Value=*********
RCC.I2C4Freq_Value=*********
RCC.IPParameters=ADCFreq_Value,AHB12Freq_Value,AHB4Freq_Value,APB1Freq_Value,APB2Freq_Value,APB3Freq_Value,APB4Freq_Value,AXIClockFreq_Value,CECFreq_Value,CKPERFreq_Value,CortexFreq_Value,CpuClockFreq_Value,D1CPREFreq_Value,D1PPRE,D2PPRE1,D2PPRE2,D3PPRE,DFSDMACLkFreq_Value,DFSDMFreq_Value,DIVM1,DIVM3,DIVN1,DIVN3,DIVP1Freq_Value,DIVP2Freq_Value,DIVP3Freq_Value,DIVQ1,DIVQ1Freq_Value,DIVQ2Freq_Value,DIVQ3,DIVQ3Freq_Value,DIVR1,DIVR1Freq_Value,DIVR2Freq_Value,DIVR3Freq_Value,EnbaleCSS,FDCANFreq_Value,FMCFreq_Value,FamilyName,HCLK3ClockFreq_Value,HCLKFreq_Value,HPRE,HPREFreq_Value,HRTIMFreq_Value,HSICalibrationValue,I2C123Freq_Value,I2C4Freq_Value,LPTIM1Freq_Value,LPTIM2Freq_Value,LPTIM345Freq_Value,LPUART1Freq_Value,LTDCFreq_Value,MCO1PinFreq_Value,MCO2PinFreq_Value,PLL2FRACN,PLL3FRACN,PLLFRACN,PLLSourceVirtual,PWR_Regulator_Voltage_Scale,QSPIFreq_Value,RNGFreq_Value,RTCFreq_Value,SAI1Freq_Value,SAI23Freq_Value,SAI4AFreq_Value,SAI4BFreq_Value,SDMMCFreq_Value,SPDIFRXFreq_Value,SPI123Freq_Value,SPI45Freq_Value,SPI6Freq_Value,SWPMI1Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,Tim1OutputFreq_Value,Tim2OutputFreq_Value,TraceFreq_Value,USART16Freq_Value,USART234578Freq_Value,USBCLockSelection,USBFreq_Value,VCO1OutputFreq_Value,VCO2OutputFreq_Value,VCO3OutputFreq_Value,VCOInput1Freq_Value,VCOInput2Freq_Value,VCOInput3Freq_Value
RCC.LPTIM1Freq_Value=*********
RCC.LPTIM2Freq_Value=*********
RCC.LPTIM345Freq_Value=*********
RCC.LPUART1Freq_Value=*********
RCC.LTDCFreq_Value=*********
RCC.MCO1PinFreq_Value=64000000
RCC.MCO2PinFreq_Value=*********
RCC.PLL2FRACN=0
RCC.PLL3FRACN=0
RCC.PLLFRACN=0
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.PWR_Regulator_Voltage_Scale=PWR_REGULATOR_VOLTAGE_SCALE1
RCC.QSPIFreq_Value=*********
RCC.RNGFreq_Value=48000000
RCC.RTCFreq_Value=32000
RCC.SAI1Freq_Value=*********
RCC.SAI23Freq_Value=*********
RCC.SAI4AFreq_Value=*********
RCC.SAI4BFreq_Value=*********
RCC.SDMMCFreq_Value=*********
RCC.SPDIFRXFreq_Value=*********
RCC.SPI123Freq_Value=*********
RCC.SPI45Freq_Value=*********
RCC.SPI6Freq_Value=*********
RCC.SWPMI1Freq_Value=*********
RCC.SYSCLKFreq_VALUE=*********
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.Tim1OutputFreq_Value=*********
RCC.Tim2OutputFreq_Value=*********
RCC.TraceFreq_Value=*********.33333333
RCC.USART16Freq_Value=*********
RCC.USART234578Freq_Value=*********
RCC.USBCLockSelection=RCC_USBCLKSOURCE_PLL3
RCC.USBFreq_Value=48000000
RCC.VCO1OutputFreq_Value=*********
RCC.VCO2OutputFreq_Value=*********
RCC.VCO3OutputFreq_Value=*********
RCC.VCOInput1Freq_Value=5000000
RCC.VCOInput2Freq_Value=781250
RCC.VCOInput3Freq_Value=1000000
SH.ADCx_INN1.0=ADC1_INN1
SH.ADCx_INN1.ConfNb=1
SH.FMC_A0.0=FMC_A0
SH.FMC_A0.ConfNb=1
SH.FMC_A1.0=FMC_A1
SH.FMC_A1.ConfNb=1
SH.FMC_A10.0=FMC_A10
SH.FMC_A10.ConfNb=1
SH.FMC_A11.0=FMC_A11
SH.FMC_A11.ConfNb=1
SH.FMC_A12.0=FMC_A12
SH.FMC_A12.ConfNb=1
SH.FMC_A13.0=FMC_A13
SH.FMC_A13.ConfNb=1
SH.FMC_A14_BA0.0=FMC_BA0
SH.FMC_A14_BA0.1=FMC_A14
SH.FMC_A14_BA0.ConfNb=2
SH.FMC_A15_BA1.0=FMC_BA1
SH.FMC_A15_BA1.1=FMC_A15
SH.FMC_A15_BA1.ConfNb=2
SH.FMC_A16_CLE.0=FMC_A16
SH.FMC_A16_CLE.ConfNb=1
SH.FMC_A17_ALE.0=FMC_A17
SH.FMC_A17_ALE.ConfNb=1
SH.FMC_A18.0=FMC_A18
SH.FMC_A18.ConfNb=1
SH.FMC_A2.0=FMC_A2
SH.FMC_A2.ConfNb=1
SH.FMC_A3.0=FMC_A3
SH.FMC_A3.ConfNb=1
SH.FMC_A4.0=FMC_A4
SH.FMC_A4.ConfNb=1
SH.FMC_A5.0=FMC_A5
SH.FMC_A5.ConfNb=1
SH.FMC_A6.0=FMC_A6
SH.FMC_A6.ConfNb=1
SH.FMC_A7.0=FMC_A7
SH.FMC_A7.ConfNb=1
SH.FMC_A8.0=FMC_A8
SH.FMC_A8.ConfNb=1
SH.FMC_A9.0=FMC_A9
SH.FMC_A9.ConfNb=1
SH.FMC_D0_DA0.0=FMC_D0
SH.FMC_D0_DA0.ConfNb=1
SH.FMC_D10_DA10.0=FMC_D10
SH.FMC_D10_DA10.ConfNb=1
SH.FMC_D11_DA11.0=FMC_D11
SH.FMC_D11_DA11.ConfNb=1
SH.FMC_D12_DA12.0=FMC_D12
SH.FMC_D12_DA12.ConfNb=1
SH.FMC_D13_DA13.0=FMC_D13
SH.FMC_D13_DA13.ConfNb=1
SH.FMC_D14_DA14.0=FMC_D14
SH.FMC_D14_DA14.ConfNb=1
SH.FMC_D15_DA15.0=FMC_D15
SH.FMC_D15_DA15.ConfNb=1
SH.FMC_D16.0=FMC_D16
SH.FMC_D16.ConfNb=1
SH.FMC_D17.0=FMC_D17
SH.FMC_D17.ConfNb=1
SH.FMC_D18.0=FMC_D18
SH.FMC_D18.ConfNb=1
SH.FMC_D19.0=FMC_D19
SH.FMC_D19.ConfNb=1
SH.FMC_D1_DA1.0=FMC_D1
SH.FMC_D1_DA1.ConfNb=1
SH.FMC_D20.0=FMC_D20
SH.FMC_D20.ConfNb=1
SH.FMC_D21.0=FMC_D21
SH.FMC_D21.ConfNb=1
SH.FMC_D22.0=FMC_D22
SH.FMC_D22.ConfNb=1
SH.FMC_D23.0=FMC_D23
SH.FMC_D23.ConfNb=1
SH.FMC_D24.0=FMC_D24
SH.FMC_D24.ConfNb=1
SH.FMC_D25.0=FMC_D25
SH.FMC_D25.ConfNb=1
SH.FMC_D26.0=FMC_D26
SH.FMC_D26.ConfNb=1
SH.FMC_D27.0=FMC_D27
SH.FMC_D27.ConfNb=1
SH.FMC_D28.0=FMC_D28
SH.FMC_D28.ConfNb=1
SH.FMC_D29.0=FMC_D29
SH.FMC_D29.ConfNb=1
SH.FMC_D2_DA2.0=FMC_D2
SH.FMC_D2_DA2.ConfNb=1
SH.FMC_D30.0=FMC_D30
SH.FMC_D30.ConfNb=1
SH.FMC_D31.0=FMC_D31
SH.FMC_D31.ConfNb=1
SH.FMC_D3_DA3.0=FMC_D3
SH.FMC_D3_DA3.ConfNb=1
SH.FMC_D4_DA4.0=FMC_D4
SH.FMC_D4_DA4.ConfNb=1
SH.FMC_D5_DA5.0=FMC_D5
SH.FMC_D5_DA5.ConfNb=1
SH.FMC_D6_DA6.0=FMC_D6
SH.FMC_D6_DA6.ConfNb=1
SH.FMC_D7_DA7.0=FMC_D7
SH.FMC_D7_DA7.ConfNb=1
SH.FMC_D8_DA8.0=FMC_D8
SH.FMC_D8_DA8.ConfNb=1
SH.FMC_D9_DA9.0=FMC_D9
SH.FMC_D9_DA9.ConfNb=1
SH.FMC_NBL0.0=FMC_NBL0
SH.FMC_NBL0.ConfNb=1
SH.FMC_NBL1.0=FMC_NBL1
SH.FMC_NBL1.ConfNb=1
SH.FMC_NBL2.0=FMC_NBL2
SH.FMC_NBL2.ConfNb=1
SH.FMC_NBL3.0=FMC_NBL3
SH.FMC_NBL3.ConfNb=1
SH.FMC_NOE.0=FMC_NOE
SH.FMC_NOE.ConfNb=1
SH.FMC_NWAIT.0=FMC_NWAIT
SH.FMC_NWAIT.ConfNb=1
SH.FMC_NWE.0=FMC_NWE
SH.FMC_NWE.ConfNb=1
SH.FMC_SDCLK.0=FMC_SDCLK
SH.FMC_SDCLK.ConfNb=1
SH.FMC_SDNCAS.0=FMC_SDNCAS
SH.FMC_SDNCAS.ConfNb=1
SH.FMC_SDNRAS.0=FMC_SDNRAS
SH.FMC_SDNRAS.ConfNb=1
SH.FMC_SDNWE.0=FMC_SDNWE
SH.FMC_SDNWE.ConfNb=1
SH.GPXTI8.0=GPIO_EXTI8
SH.GPXTI8.ConfNb=1
SH.S_CKOUTDFSDM1.0=DFSDM1_CKOUT
SH.S_CKOUTDFSDM1.ConfNb=1
SH.S_DATAIN1DFSDM1.0=DFSDM1_DATIN1
SH.S_DATAIN1DFSDM1.ConfNb=1
USB_OTG_FS.IPParameters=VirtualMode
USB_OTG_FS.VirtualMode=Device_Only
USB_OTG_HS.IPParameters=VirtualMode-Device_HS
USB_OTG_HS.VirtualMode-Device_HS=Device_HS
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
board=STM32H743I-EVAL2
boardIOC=true
