{"name": "TinyUSB", "version": "0.18.0", "description": "TinyUSB is an open-source cross-platform USB Host/Device stack for embedded system, designed to be memory-safe with no dynamic allocation and thread-safe with all interrupt events are deferred then handled in the non-ISR task function.", "keywords": "usb, host, device", "repository": {"type": "git", "url": "https://github.com/hathach/tinyusb.git"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "maintainer": true}], "license": "MIT", "homepage": "https://www.tinyusb.org/", "frameworks": "*", "platforms": "*"}