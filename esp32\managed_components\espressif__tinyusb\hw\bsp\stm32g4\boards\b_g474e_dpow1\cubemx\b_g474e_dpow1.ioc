#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.Request0=UCPD1_RX
Dma.Request1=UCPD1_TX
Dma.RequestsNb=2
Dma.UCPD1_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.UCPD1_RX.0.EventEnable=DISABLE
Dma.UCPD1_RX.0.Instance=DMA1_Channel1
Dma.UCPD1_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UCPD1_RX.0.MemInc=DMA_MINC_ENABLE
Dma.UCPD1_RX.0.Mode=DMA_NORMAL
Dma.UCPD1_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UCPD1_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.UCPD1_RX.0.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.UCPD1_RX.0.Priority=DMA_PRIORITY_HIGH
Dma.UCPD1_RX.0.RequestNumber=1
Dma.UCPD1_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.UCPD1_RX.0.SignalID=NONE
Dma.UCPD1_RX.0.SyncEnable=DISABLE
Dma.UCPD1_RX.0.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.UCPD1_RX.0.SyncRequestNumber=1
Dma.UCPD1_RX.0.SyncSignalID=NONE
Dma.UCPD1_TX.1.Direction=DMA_MEMORY_TO_PERIPH
Dma.UCPD1_TX.1.EventEnable=DISABLE
Dma.UCPD1_TX.1.Instance=DMA1_Channel2
Dma.UCPD1_TX.1.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UCPD1_TX.1.MemInc=DMA_MINC_ENABLE
Dma.UCPD1_TX.1.Mode=DMA_NORMAL
Dma.UCPD1_TX.1.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UCPD1_TX.1.PeriphInc=DMA_PINC_DISABLE
Dma.UCPD1_TX.1.Polarity=HAL_DMAMUX_REQ_GEN_RISING
Dma.UCPD1_TX.1.Priority=DMA_PRIORITY_HIGH
Dma.UCPD1_TX.1.RequestNumber=1
Dma.UCPD1_TX.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,SignalID,Polarity,RequestNumber,SyncSignalID,SyncPolarity,SyncEnable,EventEnable,SyncRequestNumber
Dma.UCPD1_TX.1.SignalID=NONE
Dma.UCPD1_TX.1.SyncEnable=DISABLE
Dma.UCPD1_TX.1.SyncPolarity=HAL_DMAMUX_SYNC_NO_EVENT
Dma.UCPD1_TX.1.SyncRequestNumber=1
Dma.UCPD1_TX.1.SyncSignalID=NONE
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=true
Mcu.CPN=STM32G474RET3
Mcu.Family=STM32G4
Mcu.IP0=DMA
Mcu.IP1=NVIC
Mcu.IP2=RCC
Mcu.IP3=SYS
Mcu.IP4=UCPD1
Mcu.IP5=USART3
Mcu.IPNb=6
Mcu.Name=STM32G474R(B-C-E)Tx
Mcu.Package=LQFP64
Mcu.Pin0=PC10
Mcu.Pin1=PC11
Mcu.Pin2=PB4
Mcu.Pin3=PB6
Mcu.Pin4=VP_SYS_VS_Systick
Mcu.Pin5=VP_SYS_VS_DBSignals
Mcu.PinsNb=6
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32G474RETx
MxCube.Version=6.8.1
MxDb.Version=DB.6.0.81
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:false\:true\:false\:false
NVIC.DMA1_Channel1_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Channel2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:false\:true\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:false\:true\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:true\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:true\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:false
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:false\:true\:false\:false
PB4.Mode=Sink_AllSignals
PB4.Signal=UCPD1_CC2
PB6.Mode=Sink_AllSignals
PB6.Signal=UCPD1_CC1
PC10.GPIOParameters=GPIO_PuPd
PC10.GPIO_PuPd=GPIO_PULLUP
PC10.Mode=Asynchronous
PC10.Signal=USART3_TX
PC11.GPIOParameters=GPIO_PuPd
PC11.GPIO_PuPd=GPIO_PULLUP
PC11.Mode=Asynchronous
PC11.Signal=USART3_RX
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32G474RETx
ProjectManager.FirmwarePackage=STM32Cube FW_G4 V1.5.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=2
ProjectManager.MainLocation=Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=b_g474e_dpow1.ioc
ProjectManager.ProjectName=b_g474e_dpow1
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=Makefile
ProjectManager.ToolChainLocation=Src
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-MX_GPIO_Init-GPIO-false-HAL-true,2-SystemClock_Config-RCC-false-HAL-false,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART3_UART_Init-USART3-false-HAL-true,5-MX_UCPD1_Init-UCPD1-false-LL-true
RCC.ADC12Freq_Value=150000000
RCC.ADC345Freq_Value=150000000
RCC.AHBFreq_Value=150000000
RCC.APB1Freq_Value=150000000
RCC.APB1TimFreq_Value=150000000
RCC.APB2Freq_Value=150000000
RCC.APB2TimFreq_Value=150000000
RCC.CRSFreq_Value=48000000
RCC.CortexFreq_Value=150000000
RCC.EXTERNAL_CLOCK_VALUE=12288000
RCC.FCLKCortexFreq_Value=150000000
RCC.FDCANFreq_Value=150000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=150000000
RCC.HRTIM1Freq_Value=150000000
RCC.HSE_VALUE=24000000
RCC.HSI48_VALUE=48000000
RCC.HSI_VALUE=16000000
RCC.I2C1Freq_Value=150000000
RCC.I2C2Freq_Value=150000000
RCC.I2C3Freq_Value=150000000
RCC.I2C4Freq_Value=150000000
RCC.I2SFreq_Value=150000000
RCC.IPParameters=ADC12Freq_Value,ADC345Freq_Value,AHBFreq_Value,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,CRSFreq_Value,CortexFreq_Value,EXTERNAL_CLOCK_VALUE,FCLKCortexFreq_Value,FDCANFreq_Value,FamilyName,HCLKFreq_Value,HRTIM1Freq_Value,HSE_VALUE,HSI48_VALUE,HSI_VALUE,I2C1Freq_Value,I2C2Freq_Value,I2C3Freq_Value,I2C4Freq_Value,I2SFreq_Value,LPTIM1Freq_Value,LPUART1Freq_Value,LSCOPinFreq_Value,LSE_VALUE,LSI_VALUE,MCO1PinFreq_Value,PLLM,PLLN,PLLPoutputFreq_Value,PLLQ,PLLQoutputFreq_Value,PLLRCLKFreq_Value,PWRFreq_Value,QSPIFreq_Value,RNGFreq_Value,SAI1Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,UART4Freq_Value,UART5Freq_Value,USART1Freq_Value,USART2Freq_Value,USART3Freq_Value,USBFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value
RCC.LPTIM1Freq_Value=150000000
RCC.LPUART1Freq_Value=150000000
RCC.LSCOPinFreq_Value=32000
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO1PinFreq_Value=16000000
RCC.PLLM=RCC_PLLM_DIV4
RCC.PLLN=75
RCC.PLLPoutputFreq_Value=150000000
RCC.PLLQ=RCC_PLLQ_DIV4
RCC.PLLQoutputFreq_Value=75000000
RCC.PLLRCLKFreq_Value=150000000
RCC.PWRFreq_Value=150000000
RCC.QSPIFreq_Value=150000000
RCC.RNGFreq_Value=75000000
RCC.SAI1Freq_Value=150000000
RCC.SYSCLKFreq_VALUE=150000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.UART4Freq_Value=150000000
RCC.UART5Freq_Value=150000000
RCC.USART1Freq_Value=150000000
RCC.USART2Freq_Value=150000000
RCC.USART3Freq_Value=150000000
RCC.USBFreq_Value=75000000
RCC.VCOInputFreq_Value=4000000
RCC.VCOOutputFreq_Value=300000000
USART3.AutoBaudRateEnableParam=UART_ADVFEATURE_AUTOBAUDRATE_DISABLE
USART3.BaudRate=115200
USART3.DMADisableonRxErrorParam=ADVFEATURE_DMA_ENABLEONRXERROR
USART3.DataInvertParam=ADVFEATURE_DATAINV_DISABLE
USART3.IPParameters=BaudRate,WordLength,Parity,StopBits,Mode,OverSampling,OneBitSampling,AutoBaudRateEnableParam,TxPinLevelInvertParam,RxPinLevelInvertParam,DataInvertParam,SwapParam,OverrunDisableParam,DMADisableonRxErrorParam,MSBFirstParam,VirtualMode-Asynchronous
USART3.MSBFirstParam=ADVFEATURE_MSBFIRST_DISABLE
USART3.Mode=MODE_TX_RX
USART3.OneBitSampling=UART_ONE_BIT_SAMPLE_DISABLE
USART3.OverSampling=UART_OVERSAMPLING_16
USART3.OverrunDisableParam=ADVFEATURE_OVERRUN_ENABLE
USART3.Parity=PARITY_ODD
USART3.RxPinLevelInvertParam=ADVFEATURE_RXINV_DISABLE
USART3.StopBits=STOPBITS_1
USART3.SwapParam=ADVFEATURE_SWAP_DISABLE
USART3.TxPinLevelInvertParam=ADVFEATURE_TXINV_DISABLE
USART3.VirtualMode-Asynchronous=VM_ASYNC
USART3.WordLength=WORDLENGTH_8B
VP_SYS_VS_DBSignals.Mode=DisableDeadBatterySignals
VP_SYS_VS_DBSignals.Signal=SYS_VS_DBSignals
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
board=custom
