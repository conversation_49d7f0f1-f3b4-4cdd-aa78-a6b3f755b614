
            /* generated memory regions file - do not edit */
                            RAM_START  = 0x22000000;
                RAM_LENGTH = 0xE0000;
                FLASH_START  = 0x02000000;
                FLASH_LENGTH = 0x1F8000;
                DATA_FLASH_START  = 0x27000000;
                DATA_FLASH_LENGTH = 0x3000;
                OPTION_SETTING_START  = 0x0300A100;
                OPTION_SETTING_LENGTH = 0x100;
                OPTION_SETTING_S_START  = 0x0300A200;
                OPTION_SETTING_S_LENGTH = 0x100;
                OPTION_SETTING_DATA_FLASH_S_START  = 0x27030080;
                OPTION_SETTING_DATA_FLASH_S_LENGTH = 0x800;
                ID_CODE_START  = 0x00000000;
                ID_CODE_LENGTH = 0x0;
                SDRAM_START  = 0x68000000;
                SDRAM_LENGTH = 0x8000000;
                QSPI_FLASH_START  = 0x60000000;
                QSPI_FLASH_LENGTH = 0x0;
                OSPI_DEVICE_0_START  = 0x80000000;
                OSPI_DEVICE_0_LENGTH = 0x10000000;
                OSPI_DEVICE_1_START  = 0x90000000;
                OSPI_DEVICE_1_LENGTH = 0x10000000;
                ITCM_START  = 0x00000000;
                ITCM_LENGTH = 0x10000;
                DTCM_START  = 0x20000000;
                DTCM_LENGTH = 0x10000;
                NS_OFFSET_START  = 0x10000000;
                NS_OFFSET_LENGTH = 0x0;
