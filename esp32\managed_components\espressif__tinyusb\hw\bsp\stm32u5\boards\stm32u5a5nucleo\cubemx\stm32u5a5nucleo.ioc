#MicroXplorer Configuration settings - do not modify
ADC1.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_2
ADC1.IPParameters=Rank-1\#ChannelRegularConversion,master,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,OffsetNumber-1\#ChannelRegularConversion,MonitoredBy-1\#ChannelRegularConversion,NbrOfConversionFlag
ADC1.MonitoredBy-1\#ChannelRegularConversion=__NULL
ADC1.NbrOfConversionFlag=1
ADC1.OffsetNumber-1\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.Rank-1\#ChannelRegularConversion=1
ADC1.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_5CYCLE
ADC1.master=1
CAD.formats=
CAD.pinconfig=
CAD.provider=
CORTEX_M33_NS.userName=CORTEX_M33
File.Version=6
GPDMA1.DIRECTION_GPDMACH0=DMA_MEMORY_TO_PERIPH
GPDMA1.DIRECTION_GPDMACH3=DMA_MEMORY_TO_PERIPH
GPDMA1.IPHANDLE_GPDMACH0-SIMPLEREQUEST_GPDMACH0=__NULL
GPDMA1.IPHANDLE_GPDMACH3-SIMPLEREQUEST_GPDMACH3=__NULL
GPDMA1.IPHANDLE_GPDMACH5-SIMPLEREQUEST_GPDMACH5=__NULL
GPDMA1.IPParameters=IPHANDLE_GPDMACH5-SIMPLEREQUEST_GPDMACH5,REQUEST_GPDMACH5,IPHANDLE_GPDMACH3-SIMPLEREQUEST_GPDMACH3,REQUEST_GPDMACH3,DIRECTION_GPDMACH3,IPHANDLE_GPDMACH0-SIMPLEREQUEST_GPDMACH0,REQUEST_GPDMACH0,DIRECTION_GPDMACH0,SRCINC_GPDMACH0
GPDMA1.REQUEST_GPDMACH0=GPDMA1_REQUEST_USART1_TX
GPDMA1.REQUEST_GPDMACH3=GPDMA1_REQUEST_UCPD1_TX
GPDMA1.REQUEST_GPDMACH5=GPDMA1_REQUEST_UCPD1_RX
GPDMA1.SRCINC_GPDMACH0=DMA_SINC_INCREMENTED
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
MMTAppReg1.MEMORYMAP.AP=RW_priv_only
MMTAppReg1.MEMORYMAP.AppRegionName=RAM
MMTAppReg1.MEMORYMAP.ContextName=CortexM33
MMTAppReg1.MEMORYMAP.CoreName=ARM Cortex-M33
MMTAppReg1.MEMORYMAP.DefaultDataRegion=true
MMTAppReg1.MEMORYMAP.IPParameters=StartAddress,Size,CoreName,DefaultDataRegion,ContextName,Name,AP
MMTAppReg1.MEMORYMAP.Name=RAM
MMTAppReg1.MEMORYMAP.Size=2555904
MMTAppReg1.MEMORYMAP.StartAddress=0x20000000
MMTAppReg2.MEMORYMAP.AppRegionName=RAM Reserved Alias Region
MMTAppReg2.MEMORYMAP.CoreName=ARM Cortex-M33
MMTAppReg2.MEMORYMAP.DefaultDataRegion=false
MMTAppReg2.MEMORYMAP.IPParameters=StartAddress,Size,CoreName,DefaultDataRegion,ReservedRegion,Name
MMTAppReg2.MEMORYMAP.Name=RAM Reserved Alias Region
MMTAppReg2.MEMORYMAP.ReservedRegion=true
MMTAppReg2.MEMORYMAP.Size=2555904
MMTAppReg2.MEMORYMAP.StartAddress=0x0A000000
MMTAppReg3.MEMORYMAP.AP=RO_priv_only
MMTAppReg3.MEMORYMAP.AppRegionName=FLASH
MMTAppReg3.MEMORYMAP.Cacheability=WTRA
MMTAppReg3.MEMORYMAP.ContextName=CortexM33
MMTAppReg3.MEMORYMAP.CoreName=ARM Cortex-M33
MMTAppReg3.MEMORYMAP.DefaultCodeRegion=true
MMTAppReg3.MEMORYMAP.DefaultDataRegion=false
MMTAppReg3.MEMORYMAP.IPParameters=StartAddress,Size,CoreName,DefaultDataRegion,MemType,ContextName,Name,AP,Cacheability,DefaultCodeRegion
MMTAppReg3.MEMORYMAP.MemType=ROM
MMTAppReg3.MEMORYMAP.Name=FLASH
MMTAppReg3.MEMORYMAP.Size=4194304
MMTAppReg3.MEMORYMAP.StartAddress=0x08000000
MMTAppRegionsCount=3
MMTConfigApplied=false
Mcu.CPN=STM32U5A5ZJT6Q
Mcu.ContextProject=TrustZoneDisabled
Mcu.Family=STM32U5
Mcu.IP0=ADC1
Mcu.IP1=CORTEX_M33_NS
Mcu.IP10=UCPD1
Mcu.IP11=USART1
Mcu.IP12=USBPD
Mcu.IP13=USBX
Mcu.IP14=USB_OTG_HS
Mcu.IP2=GPDMA1
Mcu.IP3=ICACHE
Mcu.IP4=MEMORYMAP
Mcu.IP5=NVIC
Mcu.IP6=PWR
Mcu.IP7=RCC
Mcu.IP8=SYS
Mcu.IP9=THREADX
Mcu.IPNb=15
Mcu.Name=STM32U5A5ZJTxQ
Mcu.Package=LQFP144
Mcu.Pin0=PH0-OSC_IN (PH0)
Mcu.Pin1=PH1-OSC_OUT (PH1)
Mcu.Pin10=VP_GPDMA1_VS_GPDMACH0
Mcu.Pin11=VP_GPDMA1_VS_GPDMACH3
Mcu.Pin12=VP_GPDMA1_VS_GPDMACH5
Mcu.Pin13=VP_ICACHE_VS_ICACHE
Mcu.Pin14=VP_PWR_VS_DBSignals
Mcu.Pin15=VP_PWR_VS_SECSignals
Mcu.Pin16=VP_PWR_VS_LPOM
Mcu.Pin17=VP_SYS_VS_tim6
Mcu.Pin18=VP_THREADX_VS_RTOSJjThreadXJjCoreJjDefault
Mcu.Pin19=VP_USBPD_VS_USBPD1
Mcu.Pin2=PC1
Mcu.Pin20=VP_USBPD_VS_PD3TYPEC
Mcu.Pin21=VP_USBPD_VS_usbpd_tim2
Mcu.Pin22=VP_USBPD_VS_usbpd_usb_cohabitation
Mcu.Pin23=VP_USBX_Core_System
Mcu.Pin24=VP_USBX_UX Device CoreStack_HS
Mcu.Pin25=VP_USBX_UX Device Controller_HS
Mcu.Pin26=VP_USBX_UX Device CDC ACM Class_HS
Mcu.Pin27=VP_MEMORYMAP_VS_MEMORYMAP
Mcu.Pin3=PB15
Mcu.Pin4=PG2
Mcu.Pin5=PA9
Mcu.Pin6=PA10
Mcu.Pin7=PA11
Mcu.Pin8=PA12
Mcu.Pin9=PA15 (JTDI)
Mcu.PinsNb=28
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32U5A5ZJTxQ
MxCube.Version=6.9.2
MxDb.Version=DB.6.0.92
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.GPDMA1_Channel0_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.GPDMA1_Channel3_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.GPDMA1_Channel5_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true\:true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
NVIC.OTG_HS_IRQn=true\:7\:0\:true\:false\:true\:false\:true\:true\:true
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:false\:false\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:false\:false\:false\:false\:false
NVIC.SavedPendsvIrqHandlerGenerated=true
NVIC.SavedSvcallIrqHandlerGenerated=true
NVIC.SavedSystickIrqHandlerGenerated=true
NVIC.SysTick_IRQn=true\:0\:0\:true\:false\:false\:false\:false\:true\:false
NVIC.TIM6_IRQn=true\:15\:0\:false\:false\:true\:false\:false\:true\:true
NVIC.TimeBase=TIM6_IRQn
NVIC.TimeBaseIP=TIM6
NVIC.UCPD1_IRQn=true\:5\:0\:true\:false\:true\:false\:true\:false\:true
NVIC.USART1_IRQn=true\:6\:0\:true\:false\:true\:false\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false\:false
PA10.GPIOParameters=GPIO_Speed,GPIO_PuPd
PA10.GPIO_PuPd=GPIO_PULLUP
PA10.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA11.GPIOParameters=GPIO_Speed
PA11.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PA11.Mode=Internal_Phy_Device
PA11.Signal=USB_OTG_HS_DM
PA12.GPIOParameters=GPIO_Speed
PA12.GPIO_Speed=GPIO_SPEED_FREQ_LOW
PA12.Mode=Internal_Phy_Device
PA12.Signal=USB_OTG_HS_DP
PA15\ (JTDI).Mode=Sink_AllSignals
PA15\ (JTDI).Signal=UCPD1_CC1
PA9.GPIOParameters=GPIO_Speed,GPIO_PuPd
PA9.GPIO_PuPd=GPIO_PULLUP
PA9.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB15.Mode=Sink_AllSignals
PB15.Signal=UCPD1_CC2
PC1.Mode=IN2-Single-Ended
PC1.Signal=ADC1_IN2
PG2.GPIOParameters=GPIO_Label
PG2.GPIO_Label=LED_RED
PG2.Locked=true
PG2.Signal=GPIO_Output
PH0-OSC_IN\ (PH0).Mode=HSE-External-Oscillator
PH0-OSC_IN\ (PH0).Signal=RCC_OSC_IN
PH1-OSC_OUT\ (PH1).Mode=HSE-External-Oscillator
PH1-OSC_OUT\ (PH1).Signal=RCC_OSC_OUT
PWR.IPParameters=PowerMode
PWR.PowerMode=PWR_SMPS_SUPPLY
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32U5A5ZJTxQ
ProjectManager.Example=Ux_Device_CDC_ACM
ProjectManager.ExampleSource=MxCubeFw
ProjectManager.FirmwarePackage=STM32Cube FW_U5 V1.3.0
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LPBAM.generateCode=
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=stm32u5a5nucleo.ioc
ProjectManager.ProjectName=stm32u5a5nucleo
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=STM32CubeIDE
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_GPDMA1_Init-GPDMA1-false-HAL-true,4-MX_ICACHE_Init-ICACHE-false-HAL-true,5-MX_USART1_UART_Init-USART1-false-HAL-false,6-MX_UCPD1_Init-UCPD1-false-LL-true,7-MX_USB_OTG_HS_PCD_Init-USB_OTG_HS-true-HAL-false,8-MX_USBPD_Init-USBPD-false-HAL-false,9-MX_USBX_Init-USBX-false-HAL-false,10-MX_ADC1_Init-ADC1-false-HAL-true,11-MX_MEMORYMAP_Init-MEMORYMAP-false-HAL-true,0-MX_CORTEX_M33_NS_Init-CORTEX_M33_NS-false-HAL-true,0-MX_PWR_Init-PWR-false-HAL-true
RCC.ADCFreq_Value=16000000
RCC.ADF1Freq_Value=*********
RCC.AHBFreq_Value=*********
RCC.APB1Freq_Value=*********
RCC.APB1TimFreq_Value=*********
RCC.APB2Freq_Value=*********
RCC.APB2TimFreq_Value=*********
RCC.APB3Freq_Value=*********
RCC.CK48Freq_Value=48000000
RCC.CRSFreq_Value=48000000
RCC.CortexFreq_Value=*********
RCC.DACCLockSelectionVirtual=RCC_DAC1CLKSOURCE_LSI
RCC.DACFreq_Value=32000
RCC.EPOD_VALUE=16000000
RCC.FCLKCortexFreq_Value=*********
RCC.FDCANFreq_Value=*********
RCC.FamilyName=M
RCC.HCLKFreq_Value=*********
RCC.HSE_VALUE=16000000
RCC.HSI48_VALUE=48000000
RCC.HSI_VALUE=16000000
RCC.I2C1Freq_Value=*********
RCC.I2C2Freq_Value=*********
RCC.I2C3Freq_Value=*********
RCC.I2C4Freq_Value=*********
RCC.I2C5Freq_Value=*********
RCC.I2C6Freq_Value=*********
RCC.IPParameters=ADCFreq_Value,ADF1Freq_Value,AHBFreq_Value,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,APB3Freq_Value,CK48Freq_Value,CRSFreq_Value,CortexFreq_Value,DACCLockSelectionVirtual,DACFreq_Value,EPOD_VALUE,FCLKCortexFreq_Value,FDCANFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI48_VALUE,HSI_VALUE,I2C1Freq_Value,I2C2Freq_Value,I2C3Freq_Value,I2C4Freq_Value,I2C5Freq_Value,I2C6Freq_Value,LPTIM2Freq_Value,LPUART1Freq_Value,LSCOPinFreq_Value,LSE_VALUE,LSIDIV_VALUE,LSI_VALUE,MCO1PinFreq_Value,MDF1Freq_Value,MSIClockRange,MSI_VALUE,OCTOSPIMFreq_Value,PLL1P,PLL2FRACN,PLL2PoutputFreq_Value,PLL2QoutputFreq_Value,PLL2RoutputFreq_Value,PLL3FRACN,PLL3PoutputFreq_Value,PLL3QoutputFreq_Value,PLL3RoutputFreq_Value,PLLFRACN,PLLN,PLLPoutputFreq_Value,PLLQoutputFreq_Value,PLLRCLKFreq_Value,PLLSourceVirtual,RNGFreq_Value,SAESFreq_Value,SAI1Freq_Value,SAI2Freq_Value,SDMMCFreq_Value,SPI1Freq_Value,SPI2Freq_Value,SPI3Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,UART4Freq_Value,UART5Freq_Value,USART1Freq_Value,USART2Freq_Value,USART3Freq_Value,USART6Freq_Value,USBPHYCLockSelection,USBPHYFreq_Value,VCOInput2Freq_Value,VCOInput3Freq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VCOPLL2OutputFreq_Value,VCOPLL3OutputFreq_Value
RCC.LPTIM2Freq_Value=*********
RCC.LPUART1Freq_Value=*********
RCC.LSCOPinFreq_Value=32000
RCC.LSE_VALUE=32768
RCC.LSIDIV_VALUE=32000
RCC.LSI_VALUE=32000
RCC.MCO1PinFreq_Value=*********
RCC.MDF1Freq_Value=*********
RCC.MSIClockRange=RCC_MSIRANGE_0
RCC.MSI_VALUE=48000000
RCC.OCTOSPIMFreq_Value=*********
RCC.PLL1P=8
RCC.PLL2FRACN=0
RCC.PLL2PoutputFreq_Value=3096000000
RCC.PLL2QoutputFreq_Value=3096000000
RCC.PLL2RoutputFreq_Value=3096000000
RCC.PLL3FRACN=0
RCC.PLL3PoutputFreq_Value=3096000000
RCC.PLL3QoutputFreq_Value=3096000000
RCC.PLL3RoutputFreq_Value=3096000000
RCC.PLLFRACN=0
RCC.PLLN=20
RCC.PLLPoutputFreq_Value=40000000
RCC.PLLQoutputFreq_Value=*********
RCC.PLLRCLKFreq_Value=*********
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RNGFreq_Value=48000000
RCC.SAESFreq_Value=48000000
RCC.SAI1Freq_Value=3096000000
RCC.SAI2Freq_Value=3096000000
RCC.SDMMCFreq_Value=40000000
RCC.SPI1Freq_Value=*********
RCC.SPI2Freq_Value=*********
RCC.SPI3Freq_Value=*********
RCC.SYSCLKFreq_VALUE=*********
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.UART4Freq_Value=*********
RCC.UART5Freq_Value=*********
RCC.USART1Freq_Value=*********
RCC.USART2Freq_Value=*********
RCC.USART3Freq_Value=*********
RCC.USART6Freq_Value=*********
RCC.USBPHYCLockSelection=RCC_USBPHYCLKSOURCE_HSE
RCC.USBPHYFreq_Value=16000000
RCC.VCOInput2Freq_Value=48000000
RCC.VCOInput3Freq_Value=48000000
RCC.VCOInputFreq_Value=16000000
RCC.VCOOutputFreq_Value=*********
RCC.VCOPLL2OutputFreq_Value=6192000000
RCC.VCOPLL3OutputFreq_Value=6192000000
USART1.IPParameters=VirtualMode-Asynchronous
USART1.VirtualMode-Asynchronous=VM_ASYNC
USBX.BSP.number=1
USBX.Core_System=1
USBX.IPParameters=Core_System,UX_Device_CoreStack,UX_Device_Controller,UX_DEVICE_CDC_ACM,USBD_CDCACM_EPIN_ADDR,USBD_CDCACM_EPOUT_HS_MPS,USBD_CDCACM_EPIN_HS_MPS,UX_DEVICE_APP_MEM_POOL_SIZE,USBD_PRODUCT_STRING,UX_SLAVE_REQUEST_DATA_MAX_LENGTH,USBX_DEVICE_SYS_SIZE,USBD_PID,USBD_SERIAL_NUMBER,UX_SLAVE_REQUEST_CONTROL_MAX_LENGTH,USBD_CDCACM_EPINCMD_ADDR,MAX_POWER_IN_MILLI_AMPER
USBX.MAX_POWER_IN_MILLI_AMPER=0
USBX.USBD_CDCACM_EPINCMD_ADDR=2
USBX.USBD_CDCACM_EPIN_ADDR=1
USBX.USBD_CDCACM_EPIN_HS_MPS=512
USBX.USBD_CDCACM_EPOUT_HS_MPS=512
USBX.USBD_PID=22336
USBX.USBD_PRODUCT_STRING=STM32 Virtual ComPort
USBX.USBD_SERIAL_NUMBER=CDC_ACM001
USBX.USBX_DEVICE_SYS_SIZE=4*1024
USBX.UX_DEVICE_APP_MEM_POOL_SIZE=8192
USBX.UX_DEVICE_CDC_ACM=1
USBX.UX_Device_Controller=1
USBX.UX_Device_CoreStack=1
USBX.UX_SLAVE_REQUEST_CONTROL_MAX_LENGTH=256
USBX.UX_SLAVE_REQUEST_DATA_MAX_LENGTH=512
USBX0.BSP.STBoard=false
USBX0.BSP.api=Unknown
USBX0.BSP.component=
USBX0.BSP.condition=
USBX0.BSP.instance=USB_OTG_HS
USBX0.BSP.ip=USB_OTG_HS
USBX0.BSP.mode=Device_Only
USBX0.BSP.name=USBDevice
USBX0.BSP.semaphore=
USBX0.BSP.solution=USB_OTG_HS
USB_OTG_HS.IPParameters=VirtualMode
USB_OTG_HS.VirtualMode=Device_HS
VP_GPDMA1_VS_GPDMACH0.Mode=SIMPLEREQUEST_GPDMACH0
VP_GPDMA1_VS_GPDMACH0.Signal=GPDMA1_VS_GPDMACH0
VP_GPDMA1_VS_GPDMACH3.Mode=SIMPLEREQUEST_GPDMACH3
VP_GPDMA1_VS_GPDMACH3.Signal=GPDMA1_VS_GPDMACH3
VP_GPDMA1_VS_GPDMACH5.Mode=SIMPLEREQUEST_GPDMACH5
VP_GPDMA1_VS_GPDMACH5.Signal=GPDMA1_VS_GPDMACH5
VP_ICACHE_VS_ICACHE.Mode=DirectMappedCache
VP_ICACHE_VS_ICACHE.Signal=ICACHE_VS_ICACHE
VP_MEMORYMAP_VS_MEMORYMAP.Mode=CurAppReg
VP_MEMORYMAP_VS_MEMORYMAP.Signal=MEMORYMAP_VS_MEMORYMAP
VP_PWR_VS_DBSignals.Mode=DisableDeadBatterySignals
VP_PWR_VS_DBSignals.Signal=PWR_VS_DBSignals
VP_PWR_VS_LPOM.Mode=PowerOptimisation
VP_PWR_VS_LPOM.Signal=PWR_VS_LPOM
VP_PWR_VS_SECSignals.Mode=Security/Privilege
VP_PWR_VS_SECSignals.Signal=PWR_VS_SECSignals
VP_SYS_VS_tim6.Mode=TIM6
VP_SYS_VS_tim6.Signal=SYS_VS_tim6
VP_THREADX_VS_RTOSJjThreadXJjCoreJjDefault.Mode=Core_Default
VP_THREADX_VS_RTOSJjThreadXJjCoreJjDefault.Signal=THREADX_VS_RTOSJjThreadXJjCoreJjDefault
VP_USBPD_VS_PD3TYPEC.Mode=PD3_TypeC
VP_USBPD_VS_PD3TYPEC.Signal=USBPD_VS_PD3TYPEC
VP_USBPD_VS_USBPD1.Mode=USBPD_P0
VP_USBPD_VS_USBPD1.Signal=USBPD_VS_USBPD1
VP_USBPD_VS_usbpd_tim2.Mode=TIM2
VP_USBPD_VS_usbpd_tim2.Signal=USBPD_VS_usbpd_tim2
VP_USBPD_VS_usbpd_usb_cohabitation.Mode=Enable USB Support
VP_USBPD_VS_usbpd_usb_cohabitation.Signal=USBPD_VS_usbpd_usb_cohabitation
VP_USBX_Core_System.Mode=Core_System
VP_USBX_Core_System.Signal=USBX_Core_System
VP_USBX_UX\ Device\ CDC\ ACM\ Class_HS.Mode=UX_Device_class_CDC_ACM_HS
VP_USBX_UX\ Device\ CDC\ ACM\ Class_HS.Signal=USBX_UX Device CDC ACM Class_HS
VP_USBX_UX\ Device\ Controller_HS.Mode=UX_Device_Controller_HS
VP_USBX_UX\ Device\ Controller_HS.Signal=USBX_UX Device Controller_HS
VP_USBX_UX\ Device\ CoreStack_HS.Mode=UX_Device_CoreStack_HS
VP_USBX_UX\ Device\ CoreStack_HS.Signal=USBX_UX Device CoreStack_HS
board=NUCLEO-U5A5ZJ-Q
boardIOC=true
