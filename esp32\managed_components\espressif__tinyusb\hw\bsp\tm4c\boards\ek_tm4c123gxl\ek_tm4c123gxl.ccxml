<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<configurations XML_version="1.2" id="configurations_0">
<configuration XML_version="1.2" id="configuration_0">
        <instance XML_version="1.2" desc="Stellaris In-Circuit Debug Interface" href="connections/Stellaris_ICDI_Connection.xml" id="Stellaris In-Circuit Debug Interface" xml="Stellaris_ICDI_Connection.xml" xmlpath="connections"/>
        <connection XML_version="1.2" id="Stellaris In-Circuit Debug Interface">

        		   <instance XML_version="1.2" href="drivers/stellaris_cs_dap.xml" id="drivers" xml="stellaris_cs_dap.xml" xmlpath="drivers"/>

        		   <instance XML_version="1.2" href="drivers/stellaris_cortex_m4.xml" id="drivers" xml="stellaris_cortex_m4.xml" xmlpath="drivers"/>


            <platform XML_version="1.2" id="platform_0">
                <instance XML_version="1.2" desc="Tiva TM4C123GH6PM" href="devices/tm4c123gh6pm.xml" id="Tiva TM4C123GH6PM" xml="tm4c123gh6pm.xml" xmlpath="devices"/>
            </platform>
        </connection>
    </configuration>
</configurations>
