<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<raConfiguration version="9">
  <generalSettings>
    <option key="#Board#" value="board.custom"/>
    <option key="CPU" value="RA4M1"/>
    <option key="Core" value="CM4"/>
    <option key="#TargetName#" value="R7FA4M1AB3CNE"/>
    <option key="#TargetARCHITECTURE#" value="cortex-m4"/>
    <option key="#DeviceCommand#" value="R7FA4M1AB"/>
    <option key="#RTOS#" value="_none"/>
    <option key="#pinconfiguration#" value="R7FA4M1AB3CNE.pincfg"/>
    <option key="#FSPVersion#" value="5.6.0"/>
    <option key="#SELECTED_TOOLCHAIN#" value="com.renesas.cdt.managedbuild.gnuarm.toolchain."/>
  </generalSettings>
  <raBspConfiguration>
    <config id="config.bsp.ra4m1.R7FA4M1AB3CNE">
      <property id="config.bsp.part_number" value="config.bsp.part_number.value"/>
      <property id="config.bsp.rom_size_bytes" value="config.bsp.rom_size_bytes.value"/>
      <property id="config.bsp.rom_size_bytes_hidden" value="262144"/>
      <property id="config.bsp.ram_size_bytes" value="config.bsp.ram_size_bytes.value"/>
      <property id="config.bsp.data_flash_size_bytes" value="config.bsp.data_flash_size_bytes.value"/>
      <property id="config.bsp.package_style" value="config.bsp.package_style.value"/>
      <property id="config.bsp.package_pins" value="config.bsp.package_pins.value"/>
      <property id="config.bsp.irq_count_hidden" value="32"/>
    </config>
    <config id="config.bsp.ra4m1">
      <property id="config.bsp.series" value="config.bsp.series.value"/>
    </config>
    <config id="config.bsp.ra4m1.fsp">
      <property id="config.bsp.fsp.inline_irq_functions" value="config.bsp.common.inline_irq_functions.enabled"/>
      <property id="config.bsp.fsp.OFS0.iwdt_start_mode" value="config.bsp.fsp.OFS0.iwdt_start_mode.disabled"/>
      <property id="config.bsp.fsp.OFS0.iwdt_timeout" value="config.bsp.fsp.OFS0.iwdt_timeout.2048"/>
      <property id="config.bsp.fsp.OFS0.iwdt_divisor" value="config.bsp.fsp.OFS0.iwdt_divisor.128"/>
      <property id="config.bsp.fsp.OFS0.iwdt_window_end" value="config.bsp.fsp.OFS0.iwdt_window_end.0"/>
      <property id="config.bsp.fsp.OFS0.iwdt_window_start" value="config.bsp.fsp.OFS0.iwdt_window_start.100"/>
      <property id="config.bsp.fsp.OFS0.iwdt_reset_interrupt" value="config.bsp.fsp.OFS0.iwdt_reset_interrupt.Reset"/>
      <property id="config.bsp.fsp.OFS0.iwdt_stop_control" value="config.bsp.fsp.OFS0.iwdt_stop_control.stops"/>
      <property id="config.bsp.fsp.OFS0.wdt_start_mode" value="config.bsp.fsp.OFS0.wdt_start_mode.register"/>
      <property id="config.bsp.fsp.OFS0.wdt_timeout" value="config.bsp.fsp.OFS0.wdt_timeout.16384"/>
      <property id="config.bsp.fsp.OFS0.wdt_divisor" value="config.bsp.fsp.OFS0.wdt_divisor.128"/>
      <property id="config.bsp.fsp.OFS0.wdt_window_end" value="config.bsp.fsp.OFS0.wdt_window_end.0"/>
      <property id="config.bsp.fsp.OFS0.wdt_window_start" value="config.bsp.fsp.OFS0.wdt_window_start.100"/>
      <property id="config.bsp.fsp.OFS0.wdt_reset_interrupt" value="config.bsp.fsp.OFS0.wdt_reset_interrupt.Reset"/>
      <property id="config.bsp.fsp.OFS0.wdt_stop_control" value="config.bsp.fsp.OFS0.wdt_stop_control.stops"/>
      <property id="config.bsp.fsp.OFS1.voltage_detection0.start" value="config.bsp.fsp.OFS1.voltage_detection0.start.disabled"/>
      <property id="config.bsp.fsp.OFS1.voltage_detection0_level" value="config.bsp.fsp.OFS1.voltage_detection0_level.190"/>
      <property id="config.bsp.fsp.OFS1.hoco_osc" value="config.bsp.fsp.OFS1.hoco_osc.enabled"/>
      <property id="config.bsp.low_voltage_mode" value="config.bsp.low_voltage_mode.disabled"/>
      <property id="config.bsp.fsp.mpu_pc0_enable" value="config.bsp.fsp.mpu_pc0_enable.disabled"/>
      <property id="config.bsp.fsp.mpu_pc0_start" value="0x00FFFFFC"/>
      <property id="config.bsp.fsp.mpu_pc0_end" value="0x00FFFFFF"/>
      <property id="config.bsp.fsp.mpu_pc1_enable" value="config.bsp.fsp.mpu_pc1_enable.disabled"/>
      <property id="config.bsp.fsp.mpu_pc1_start" value="0x00FFFFFC"/>
      <property id="config.bsp.fsp.mpu_pc1_end" value="0x00FFFFFF"/>
      <property id="config.bsp.fsp.mpu_reg0_enable" value="config.bsp.fsp.mpu_reg0_enable.disabled"/>
      <property id="config.bsp.fsp.mpu_reg0_start" value="0x00FFFFFC"/>
      <property id="config.bsp.fsp.mpu_reg0_end" value="0x00FFFFFF"/>
      <property id="config.bsp.fsp.mpu_reg1_enable" value="config.bsp.fsp.mpu_reg1_enable.disabled"/>
      <property id="config.bsp.fsp.mpu_reg1_start" value="0x200FFFFC"/>
      <property id="config.bsp.fsp.mpu_reg1_end" value="0x200FFFFF"/>
      <property id="config.bsp.fsp.mpu_reg2_enable" value="config.bsp.fsp.mpu_reg2_enable.disabled"/>
      <property id="config.bsp.fsp.mpu_reg2_start" value="0x407FFFFC"/>
      <property id="config.bsp.fsp.mpu_reg2_end" value="0x407FFFFF"/>
      <property id="config.bsp.fsp.mpu_reg3_enable" value="config.bsp.fsp.mpu_reg3_enable.disabled"/>
      <property id="config.bsp.fsp.mpu_reg3_start" value="0x400DFFFC"/>
      <property id="config.bsp.fsp.mpu_reg3_end" value="0x400DFFFF"/>
      <property id="config.bsp.common.main_osc_wait" value="config.bsp.common.main_osc_wait.wait_8163"/>
      <property id="config.bsp.fsp.mcu.adc.max_freq_hz" value="64000000"/>
      <property id="config.bsp.fsp.mcu.sci_uart.max_baud" value="6666666"/>
      <property id="config.bsp.fsp.mcu.adc.sample_and_hold" value="0"/>
      <property id="config.bsp.fsp.mcu.adc.sensors_are_exclusive" value="1"/>
      <property id="config.bsp.fsp.mcu.sci_spi.max_bitrate" value="12000000"/>
      <property id="config.bsp.fsp.mcu.spi.max_bitrate" value="24000000"/>
      <property id="config.bsp.fsp.mcu.iic_master.rate.rate_fastplus" value="0"/>
      <property id="config.bsp.fsp.mcu.iic_master.fastplus_channels" value="0"/>
      <property id="config.bsp.fsp.mcu.iic_slave.rate.rate_fastplus" value="0"/>
      <property id="config.bsp.fsp.mcu.iic_slave.fastplus_channels" value="0x0"/>
      <property id="config.bsp.fsp.mcu.sci_uart.cstpen_channels" value="0x0"/>
      <property id="config.bsp.fsp.mcu.gpt.pin_count_source_channels" value="0xFFFF"/>
      <property id="config.bsp.fsp.mcu.slcdc.1_4_bias_method" value="1"/>
      <property id="config.bsp.common.id_mode" value="config.bsp.common.id_mode.unlocked"/>
      <property id="config.bsp.common.id_code" value="FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF"/>
      <property id="config.bsp.common.id1" value=""/>
      <property id="config.bsp.common.id2" value=""/>
      <property id="config.bsp.common.id3" value=""/>
      <property id="config.bsp.common.id4" value=""/>
      <property id="config.bsp.common.id_fixed" value=""/>
    </config>
    <config id="config.bsp.ra">
      <property id="config.bsp.common.main" value="0x800"/>
      <property id="config.bsp.common.heap" value="0x1000"/>
      <property id="config.bsp.common.vcc" value="3300"/>
      <property id="config.bsp.common.checking" value="config.bsp.common.checking.disabled"/>
      <property id="config.bsp.common.assert" value="config.bsp.common.assert.none"/>
      <property id="config.bsp.common.error_log" value="config.bsp.common.error_log.none"/>
      <property id="config.bsp.common.soft_reset" value="config.bsp.common.soft_reset.disabled"/>
      <property id="config.bsp.common.main_osc_populated" value="config.bsp.common.main_osc_populated.disabled"/>
      <property id="config.bsp.common.pfs_protect" value="config.bsp.common.pfs_protect.enabled"/>
      <property id="config.bsp.common.c_runtime_init" value="config.bsp.common.c_runtime_init.enabled"/>
      <property id="config.bsp.common.early_init" value="config.bsp.common.early_init.disabled"/>
      <property id="config.bsp.common.main_osc_clock_source" value="config.bsp.common.main_osc_clock_source.crystal"/>
      <property id="config.bsp.common.subclock_populated" value="config.bsp.common.subclock_populated.disabled"/>
      <property id="config.bsp.common.subclock_drive" value="config.bsp.common.subclock_drive.standard"/>
      <property id="config.bsp.common.subclock_stabilization_ms" value="1000"/>
    </config>
  </raBspConfiguration>
  <raClockConfiguration>
    <node id="board.clock.xtal.freq" mul="0" option="_edit"/>
    <node id="board.clock.pll.source" option="board.clock.pll.source.disabled"/>
    <node id="board.clock.hoco.freq" option="board.clock.hoco.freq.48m"/>
    <node id="board.clock.loco.freq" option="board.clock.loco.freq.32768"/>
    <node id="board.clock.moco.freq" option="board.clock.moco.freq.8m"/>
    <node id="board.clock.subclk.freq" option="board.clock.subclk.freq.32768"/>
    <node id="board.clock.pll.div" option="board.clock.pll.div.4"/>
    <node id="board.clock.pll.mul" option="board.clock.pll.mul.12"/>
    <node id="board.clock.pll.display" option="board.clock.pll.display.value"/>
    <node id="board.clock.clock.source" option="board.clock.clock.source.hoco"/>
    <node id="board.clock.iclk.div" option="board.clock.iclk.div.1"/>
    <node id="board.clock.iclk.display" option="board.clock.iclk.display.value"/>
    <node id="board.clock.pclka.div" option="board.clock.pclka.div.1"/>
    <node id="board.clock.pclka.display" option="board.clock.pclka.display.value"/>
    <node id="board.clock.pclkb.div" option="board.clock.pclkb.div.2"/>
    <node id="board.clock.pclkb.display" option="board.clock.pclkb.display.value"/>
    <node id="board.clock.pclkc.div" option="board.clock.pclkc.div.1"/>
    <node id="board.clock.pclkc.display" option="board.clock.pclkc.display.value"/>
    <node id="board.clock.pclkd.div" option="board.clock.pclkd.div.1"/>
    <node id="board.clock.pclkd.display" option="board.clock.pclkd.display.value"/>
    <node id="board.clock.fclk.div" option="board.clock.fclk.div.2"/>
    <node id="board.clock.fclk.display" option="board.clock.fclk.display.value"/>
    <node id="board.clock.clkout.source" option="board.clock.clkout.source.disabled"/>
    <node id="board.clock.clkout.div" option="board.clock.clkout.div.1"/>
    <node id="board.clock.clkout.display" option="board.clock.clkout.display.value"/>
    <node id="board.clock.uclk.source" option="board.clock.uclk.source.hoco"/>
    <node id="board.clock.uclk.display" option="board.clock.clkout.display.value"/>
  </raClockConfiguration>
  <raComponentSelection>
    <component apiversion="" class="Common" condition="" group="all" subgroup="fsp_common" variant="" vendor="Renesas" version="5.6.0">
      <description>Board Support Package Common Files</description>
      <originalPack>Renesas.RA.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="HAL Drivers" condition="" group="all" subgroup="r_ioport" variant="" vendor="Renesas" version="5.6.0">
      <description>I/O Port</description>
      <originalPack>Renesas.RA.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="CMSIS" condition="" group="CMSIS5" subgroup="CoreM" variant="" vendor="Arm" version="6.1.0+fsp.5.6.0">
      <description>Arm CMSIS Version 6 - Core (M)</description>
      <originalPack>Arm.CMSIS6.6.1.0+fsp.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="Board" subgroup="custom" variant="" vendor="Renesas" version="5.6.0">
      <description>Custom Board Support Files</description>
      <originalPack>Renesas.RA_board_custom.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="ra4m1" subgroup="device" variant="R7FA4M1AB3CNE" vendor="Renesas" version="5.6.0">
      <description>Board support package for R7FA4M1AB3CNE</description>
      <originalPack>Renesas.RA_mcu_ra4m1.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="ra4m1" subgroup="device" variant="" vendor="Renesas" version="5.6.0">
      <description>Board support package for RA4M1</description>
      <originalPack>Renesas.RA_mcu_ra4m1.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="ra4m1" subgroup="fsp" variant="" vendor="Renesas" version="5.6.0">
      <description>Board support package for RA4M1 - FSP Data</description>
      <originalPack>Renesas.RA_mcu_ra4m1.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="ra4m1" subgroup="events" variant="" vendor="Renesas" version="5.6.0">
      <description>Board support package for RA4M1 - Events</description>
      <originalPack>Renesas.RA_mcu_ra4m1.5.6.0.pack</originalPack>
    </component>
  </raComponentSelection>
  <raElcConfiguration/>
  <raIcuConfiguration/>
  <raModuleConfiguration>
    <module id="module.driver.ioport_on_ioport.0">
      <property id="module.driver.ioport.name" value="g_ioport"/>
      <property id="module.driver.ioport.elc_trigger_ioport1" value="_disabled"/>
      <property id="module.driver.ioport.elc_trigger_ioport2" value="_disabled"/>
      <property id="module.driver.ioport.elc_trigger_ioport3" value="_disabled"/>
      <property id="module.driver.ioport.elc_trigger_ioport4" value="_disabled"/>
      <property id="module.driver.ioport.pincfg" value="g_bsp_pin_cfg"/>
    </module>
    <context id="_hal.0">
      <stack module="module.driver.ioport_on_ioport.0"/>
    </context>
    <config id="config.driver.ioport">
      <property id="config.driver.ioport.checking" value="config.driver.ioport.checking.system"/>
    </config>
  </raModuleConfiguration>
  <raPinConfiguration>
    <symbolicName propertyId="p110.symbolic_name" value="SW1"/>
    <symbolicName propertyId="p111.symbolic_name" value="LED1"/>
    <comment propertyId="p110.comment" value="active low"/>
    <comment propertyId="p111.comment" value="active high"/>
    <pincfg active="true" name="R7FA4M1AB3CNE.pincfg" selected="true" symbol="g_bsp_pin_cfg">
      <configSetting altId="debug0.mode.swd" configurationId="debug0.mode"/>
      <configSetting altId="debug0.swclk.p300" configurationId="debug0.swclk"/>
      <configSetting altId="debug0.swdio.p108" configurationId="debug0.swdio"/>
      <configSetting altId="p108.debug0.swdio" configurationId="p108"/>
      <configSetting altId="p108.gpio_mode.gpio_mode_peripheral" configurationId="p108.gpio_mode"/>
      <configSetting altId="p110.input" configurationId="p110"/>
      <configSetting altId="p110.gpio_mode.gpio_mode_in" configurationId="p110.gpio_mode"/>
      <configSetting altId="p110.gpio_pupd.gpio_pupd_ip_up" configurationId="p110.gpio_pupd"/>
      <configSetting altId="p111.output.low" configurationId="p111"/>
      <configSetting altId="p111.gpio_mode.gpio_mode_out.low" configurationId="p111.gpio_mode"/>
      <configSetting altId="p300.debug0.swclk" configurationId="p300"/>
      <configSetting altId="p300.gpio_mode.gpio_mode_peripheral" configurationId="p300.gpio_mode"/>
      <configSetting altId="p407.usbfs0.vbus" configurationId="p407"/>
      <configSetting altId="p407.gpio_mode.gpio_mode_peripheral" configurationId="p407.gpio_mode"/>
      <configSetting altId="p914.usbfs0.usbdp" configurationId="p914"/>
      <configSetting altId="p914.gpio_mode.gpio_mode_peripheral" configurationId="p914.gpio_mode"/>
      <configSetting altId="p915.usbfs0.usbdm" configurationId="p915"/>
      <configSetting altId="p915.gpio_mode.gpio_mode_peripheral" configurationId="p915.gpio_mode"/>
      <configSetting altId="usbfs0.mode.device" configurationId="usbfs0.mode"/>
      <configSetting altId="usbfs0.usbdm.p915" configurationId="usbfs0.usbdm"/>
      <configSetting altId="usbfs0.usbdp.p914" configurationId="usbfs0.usbdp"/>
      <configSetting altId="usbfs0.vbus.p407" configurationId="usbfs0.vbus"/>
    </pincfg>
  </raPinConfiguration>
</raConfiguration>
