#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于RKNN多NPU的高性能垃圾检测器
集成detect_v2.py的优化逻辑到现有框架
"""

import time
import numpy as np
import cv2
import threading
import logging
from queue import Queue, Empty
from collections import deque
from typing import Optional, Dict, Any, List
from datetime import datetime

# 尝试导入RKNN
try:
    from rknnlite.api import RKNNLite
    RKNN_AVAILABLE = True
except ImportError:
    RKNN_AVAILABLE = False
    print("❌ 错误：RKNN不可用，请安装rknnlite库")

logger = logging.getLogger(__name__)

# RKNN检测参数
RKNN_MODEL = 'detect/rknn-0804.rknn'  # RKNN模型路径
OBJ_THRESH = 0.65  # 提高置信度阈值，减少误检
NMS_THRESH = 0.45
IMG_SIZE = 640

CLASSES = ("harmful", "recyclable", "kitchen", "other")

def sigmoid(x):
    return 1 / (1 + np.exp(-x))

def xywh2xyxy(x):
    y = np.copy(x)
    y[:, 0] = x[:, 0] - x[:, 2] / 2
    y[:, 1] = x[:, 1] - x[:, 3] / 2
    y[:, 2] = x[:, 0] + x[:, 2] / 2
    y[:, 3] = x[:, 1] + x[:, 3] / 2
    return y

def process(input, mask, anchors):
    anchors = [anchors[i] for i in mask]
    grid_h, grid_w = map(int, input.shape[0:2])
    box_confidence = sigmoid(input[..., 4])
    box_confidence = np.expand_dims(box_confidence, axis=-1)
    box_class_probs = sigmoid(input[..., 5:])
    box_xy = sigmoid(input[..., :2])*2 - 0.5
    col = np.tile(np.arange(0, grid_w), grid_w).reshape(-1, grid_w)
    row = np.tile(np.arange(0, grid_h).reshape(-1, 1), grid_h)
    col = col.reshape(grid_h, grid_w, 1, 1).repeat(3, axis=-2)
    row = row.reshape(grid_h, grid_w, 1, 1).repeat(3, axis=-2)
    grid = np.concatenate((col, row), axis=-1)
    box_xy += grid
    box_xy *= int(IMG_SIZE/grid_h)
    box_wh = pow(sigmoid(input[..., 2:4])*2, 2)
    box_wh = box_wh * anchors
    box = np.concatenate((box_xy, box_wh), axis=-1)
    return box, box_confidence, box_class_probs

def filter_boxes(boxes, box_confidences, box_class_probs):
    boxes = boxes.reshape(-1, 4)
    box_confidences = box_confidences.reshape(-1)
    box_class_probs = box_class_probs.reshape(-1, box_class_probs.shape[-1])
    _box_pos = np.where(box_confidences >= OBJ_THRESH)
    boxes = boxes[_box_pos]
    box_confidences = box_confidences[_box_pos]
    box_class_probs = box_class_probs[_box_pos]
    class_max_score = np.max(box_class_probs, axis=-1)
    classes = np.argmax(box_class_probs, axis=-1)
    _class_pos = np.where(class_max_score >= OBJ_THRESH)
    boxes = boxes[_class_pos]
    classes = classes[_class_pos]
    scores = (class_max_score* box_confidences)[_class_pos]
    return boxes, classes, scores

def nms_boxes_optimized(boxes, scores, max_boxes=50):
    """优化的NMS算法"""
    if len(boxes) == 0:
        return np.array([])

    if len(scores) > max_boxes:
        top_indices = np.argpartition(scores, -max_boxes)[-max_boxes:]
        boxes = boxes[top_indices]
        scores = scores[top_indices]

    x1 = boxes[:, 0]
    y1 = boxes[:, 1]
    x2 = boxes[:, 2]
    y2 = boxes[:, 3]
    areas = (x2 - x1) * (y2 - y1)
    order = scores.argsort()[::-1]

    keep = []
    while order.size > 0:
        i = order[0]
        keep.append(i)

        if len(keep) >= 20:  # 限制最终输出数量
            break

        xx1 = np.maximum(x1[i], x1[order[1:]])
        yy1 = np.maximum(y1[i], y1[order[1:]])
        xx2 = np.minimum(x2[i], x2[order[1:]])
        yy2 = np.minimum(y2[i], y2[order[1:]])

        w = np.maximum(0.0, xx2 - xx1)
        h = np.maximum(0.0, yy2 - yy1)
        inter = w * h
        ovr = inter / (areas[i] + areas[order[1:]] - inter)

        inds = np.where(ovr <= NMS_THRESH)[0]
        order = order[inds + 1]

    return np.array(keep)

def yolov5_post_process_optimized(input_data, max_detections=200):
    """优化的后处理函数"""
    masks = [[0, 1, 2], [3, 4, 5], [6, 7, 8]]
    anchors = [[10, 13], [16, 30], [33, 23], [30, 61], [62, 45],
               [59, 119], [116, 90], [156, 198], [373, 326]]

    all_boxes, all_classes, all_scores = [], [], []

    for input, mask in zip(input_data, masks):
        b, c, s = process(input, mask, anchors)
        b, c, s = filter_boxes(b, c, s)

        if len(s) > 0:
            high_conf_mask = s > (OBJ_THRESH + 0.1)
            if np.any(high_conf_mask):
                all_boxes.append(b[high_conf_mask])
                all_classes.append(c[high_conf_mask])
                all_scores.append(s[high_conf_mask])

    if not all_boxes:
        return None, None, None

    boxes = np.concatenate(all_boxes)
    classes = np.concatenate(all_classes)
    scores = np.concatenate(all_scores)

    if len(scores) > max_detections:
        top_indices = np.argpartition(scores, -max_detections)[-max_detections:]
        boxes = boxes[top_indices]
        classes = classes[top_indices]
        scores = scores[top_indices]

    boxes = xywh2xyxy(boxes)

    # 按类别进行NMS
    final_boxes, final_classes, final_scores = [], [], []
    unique_classes = np.unique(classes)

    for cls in unique_classes:
        cls_mask = classes == cls
        cls_boxes = boxes[cls_mask]
        cls_scores = scores[cls_mask]

        max_per_class = min(10, len(cls_scores))  # 每类最多10个
        if len(cls_scores) > max_per_class:
            top_indices = np.argpartition(cls_scores, -max_per_class)[-max_per_class:]
            cls_boxes = cls_boxes[top_indices]
            cls_scores = cls_scores[top_indices]

        keep = nms_boxes_optimized(cls_boxes, cls_scores)
        if len(keep) > 0:
            final_boxes.append(cls_boxes[keep])
            final_classes.append(np.full(len(keep), cls))
            final_scores.append(cls_scores[keep])

    if not final_boxes:
        return None, None, None

    final_boxes = np.concatenate(final_boxes)
    final_classes = np.concatenate(final_classes)
    final_scores = np.concatenate(final_scores)

    return final_boxes, final_classes, final_scores

def letterbox(im, new_shape=(640, 640), color=(0, 0, 0)):
    shape = im.shape[:2]
    if isinstance(new_shape, int):
        new_shape = (new_shape, new_shape)
    r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
    ratio = r, r
    new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
    dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]
    dw /= 2
    dh /= 2
    if shape[::-1] != new_unpad:
        im = cv2.resize(im, new_unpad, interpolation=cv2.INTER_LINEAR)
    top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
    left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
    im = cv2.copyMakeBorder(im, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)
    return im, ratio, (dw, dh)

class MultiNPUDetector:
    """多NPU检测器管理类"""
    def __init__(self, model_path, num_cores=3):
        self.model_path = model_path
        self.rknn_instances = []
        self.available_instances = Queue()
        
        if not RKNN_AVAILABLE:
            logger.error("❌ RKNN不可用，无法初始化NPU")
            return
            
        core_masks = [RKNNLite.NPU_CORE_0, RKNNLite.NPU_CORE_1, RKNNLite.NPU_CORE_2]
        
        logger.info(f"正在初始化 {num_cores} 个NPU核心...")
        
        for i in range(min(num_cores, len(core_masks))):
            try:
                rknn = RKNNLite()
                logger.info(f'  --> 在NPU核心 {i} 上加载模型')
                ret = rknn.load_rknn(model_path)
                if ret != 0:
                    logger.error(f'  --> NPU核心 {i} 模型加载失败!')
                    continue
                    
                logger.info(f'  --> 初始化NPU核心 {i} 运行环境')
                ret = rknn.init_runtime(core_mask=core_masks[i])
                if ret != 0:
                    logger.error(f'  --> NPU核心 {i} 运行环境初始化失败!')
                    rknn.release()
                    continue
                    
                self.rknn_instances.append(rknn)
                self.available_instances.put(rknn)
                logger.info(f'  --> NPU核心 {i} 初始化成功')
            except Exception as e:
                logger.error(f"NPU核心 {i} 初始化失败: {e}")
        
        logger.info(f"成功初始化 {len(self.rknn_instances)} 个NPU核心")
        
        if len(self.rknn_instances) == 0 and RKNN_AVAILABLE:
            logger.error("❌ 没有NPU核心初始化成功，检测器无法工作")
    
    def get_instance(self):
        if not RKNN_AVAILABLE or len(self.rknn_instances) == 0:
            return None
        return self.available_instances.get()
    
    def return_instance(self, rknn):
        if rknn is not None:
            self.available_instances.put(rknn)
    
    def release_all(self):
        for rknn in self.rknn_instances:
            try:
                rknn.release()
            except:
                pass

class RKNNDetector:
    """兼容现有接口的RKNN检测器"""
    
    def __init__(self, model_name: str = "rknn-0804", img_size: int = 640, conf_thres: float = 0.4):
        """
        初始化RKNN检测器
        
        Args:
            model_name: 模型名称
            img_size: 输入图像尺寸
            conf_thres: 置信度阈值
        """
        self.model_name = model_name
        self.img_size = img_size
        self.conf_thres = conf_thres
        self.iou_thres = NMS_THRESH
        
        self.is_loaded = False
        self.is_detecting = False
        
        # 检测结果
        self.last_detection = None
        self.detection_confidence = 0.0
        
        # 垃圾类型映射 - 与现有系统保持一致
        self.class_mapping = {
            0: {'name': 'harmful', 'display': '有害垃圾', 'id': 2},
            1: {'name': 'recyclable', 'display': '可回收垃圾', 'id': 1},
            2: {'name': 'kitchen', 'display': '厨余垃圾', 'id': 3},
            3: {'name': 'other', 'display': '其他垃圾', 'id': 4}
        }
        
        # ROI区域配置 - 适配640x640摄像头尺寸，调整高度分布
        self.roi_areas = [
            {
                'name': '检测区域',
                'area': (0, 120, 640, 630),  # 调整高度：上边界120，下边界630（往下30像素）
                'color': (255, 0, 0),
                'classes': ['all'],
                'purpose': 'detection'
            },
            {
                'name': '左分拣区',
                'area': (0, 120, 160, 630),  # 左侧区域：上边界120，下边界630
                'color': (0, 255, 0),
                'classes': ['harmful', 'other'],
                'purpose': 'position'
            },
            {
                'name': '右分拣区',
                'area': (480, 120, 640, 630),  # 右侧区域：上边界120，下边界630
                'color': (0, 255, 255),
                'classes': ['recyclable', 'kitchen'],
                'purpose': 'position'
            }
        ]
        
        # 检测统计
        self.detection_stats = {
            'total_detections': 0,
            'successful_detections': 0,
            'failed_detections': 0,
            'average_confidence': 0.0
        }
        
        # 多NPU检测器
        self.multi_npu_detector = None
        
        # 异步处理相关
        self.img_queue = None
        self.raw_result_queue = None
        self.final_result_queue = None
        self.inference_threads = []
        self.postprocess_threads = []

        # 性能统计
        self.frame_stats = {
            'submitted': 0,
            'processed': 0,
            'detected': 0,
            'dropped': 0
        }
        self.frame_count = 0

        # 外部状态检查器 - 用于GUI状态控制
        self.external_state_checker = None  # 添加帧计数器用于统计打印
        
        # 画面回调
        self.frame_callback = None
        
        logger.info("RKNN检测器初始化完成")
        logger.info("类别映射:")
        for class_id, info in self.class_mapping.items():
            logger.info(f"  类别ID {class_id}: {info['name']} -> {info['display']}")

    def set_frame_callback(self, callback):
        """设置画面回调函数"""
        self.frame_callback = callback
        logger.info("画面回调函数已设置")

    def load_model(self) -> bool:
        """加载RKNN模型"""
        try:
            model_path = RKNN_MODEL
            
            if not RKNN_AVAILABLE:
                logger.error("❌ RKNN不可用，无法加载模型")
                self.is_loaded = False
                return False
            
            logger.info(f"正在加载RKNN模型: {model_path}")
            
            # 初始化多NPU检测器
            self.multi_npu_detector = MultiNPUDetector(model_path, num_cores=3)

            # 检查NPU实例数量
            npu_count = len(self.multi_npu_detector.rknn_instances)
            logger.info(f"成功初始化 {npu_count} 个NPU实例")

            if npu_count == 0:
                logger.error("没有NPU实例初始化成功，无法进行检测")
                self.is_loaded = False
                return False

            # 设置异步处理队列 - 优化队列大小防止内存积累
            max_workers = max(1, npu_count)
            # 严格限制队列大小，防止内存积累
            self.img_queue = Queue(maxsize=2)  # 最小输入队列，防止帧积累
            self.raw_result_queue = Queue(maxsize=2)  # 最小原始结果队列
            self.final_result_queue = Queue(maxsize=1)  # 最小最终结果队列，保持最高实时性

            logger.info(f"设置队列大小: img_queue=2, raw_result_queue=2, final_result_queue=1 (内存优化)")

            self.is_loaded = True
            logger.info("RKNN模型加载成功")
            return True
            
        except Exception as e:
            logger.error(f"RKNN模型加载失败: {e}")
            self.is_loaded = False
            return False

    def start_detection(self) -> bool:
        """开始检测"""
        if not self.is_loaded:
            logger.error("RKNN模型未加载，无法开始检测")
            return False
        
        if self.is_detecting:
            logger.warning("检测已在进行中")
            return True
        
        # 先设置检测状态，避免竞态条件
        self.is_detecting = True

        # 启动异步处理线程
        if RKNN_AVAILABLE and self.multi_npu_detector and len(self.multi_npu_detector.rknn_instances) > 0:
            self._start_async_processing()

        logger.info("RKNN检测已启动")
        return True

    def stop_detection(self):
        """停止检测"""
        self.is_detecting = False

        # 停止异步处理线程
        if RKNN_AVAILABLE and self.inference_threads:
            self._stop_async_processing()

        # 清理队列中的积累数据
        self._clear_queues()

        logger.info("RKNN检测已停止")

    def _start_async_processing(self):
        """启动异步处理线程"""
        max_workers = len(self.multi_npu_detector.rknn_instances)
        
        # 启动推理线程
        for i in range(max_workers):
            t = threading.Thread(target=self._inference_worker, args=(i,), daemon=True)
            t.start()
            self.inference_threads.append(t)
        
        # 启动后处理线程 - 减少线程数量优化资源使用
        num_postprocess_workers = 1  # 固定使用1个后处理线程，减少资源占用
        for i in range(num_postprocess_workers):
            t = threading.Thread(target=self._postprocess_worker, args=(i,), daemon=True)
            t.start()
            self.postprocess_threads.append(t)

        logger.info(f"启动了 {max_workers} 个推理线程和 {num_postprocess_workers} 个后处理线程 (资源优化)")

    def _stop_async_processing(self):
        """停止异步处理线程"""
        # 发送停止信号
        for _ in self.inference_threads:
            try:
                self.img_queue.put(None, timeout=1)
            except:
                pass
        
        for _ in self.postprocess_threads:
            try:
                self.raw_result_queue.put(None, timeout=1)
            except:
                pass
        
        # 等待线程结束
        for t in self.inference_threads:
            t.join(timeout=2)
        for t in self.postprocess_threads:
            t.join(timeout=2)
        
        self.inference_threads.clear()
        self.postprocess_threads.clear()

    def _clear_queues(self):
        """清理队列中的积累数据，防止内存泄漏"""
        try:
            # 清空输入队列
            while not self.img_queue.empty():
                try:
                    self.img_queue.get_nowait()
                except:
                    break

            # 清空原始结果队列
            while not self.raw_result_queue.empty():
                try:
                    self.raw_result_queue.get_nowait()
                except:
                    break

            # 清空最终结果队列
            while not self.final_result_queue.empty():
                try:
                    self.final_result_queue.get_nowait()
                except:
                    break

            logger.info("已清理检测器队列数据")
        except Exception as e:
            logger.error(f"清理队列数据失败: {e}")

    def _inference_worker(self, worker_id):
        """推理工作线程 - 完全按照detect_v2.py的架构"""
        logger.info(f"推理工作线程 {worker_id} 启动")

        # 获取NPU实例（每个线程独占一个NPU实例）
        rknn = self.multi_npu_detector.get_instance() if self.multi_npu_detector else None

        if rknn is None:
            logger.error(f"推理工作线程 {worker_id}: 无法获取RKNN实例")
            return

        while True:
            try:
                frame_data = self.img_queue.get(timeout=0.1)
                if frame_data is None:
                    logger.info(f"推理工作线程 {worker_id}: 收到停止信号")
                    break

                frame_id, img = frame_data
                start_time = time.time()

                # 只做NPU推理
                outputs = rknn.inference(inputs=[img])
                inference_time = time.time() - start_time

                # 立即放入原始结果队列
                self.raw_result_queue.put((frame_id, outputs, inference_time, worker_id))
                self.img_queue.task_done()

            except Empty:
                continue
            except Exception as e:
                logger.error(f"推理线程 {worker_id} 出错: {e}")
                break

        if self.multi_npu_detector:
            self.multi_npu_detector.return_instance(rknn)
        logger.info(f"推理工作线程 {worker_id} 退出")

    def _postprocess_worker(self, worker_id):
        """后处理工作线程 - 完全按照detect_v2.py的架构"""
        logger.info(f"后处理线程 {worker_id} 启动")

        while True:
            try:
                raw_data = self.raw_result_queue.get(timeout=1)
                if raw_data is None:
                    logger.info(f"后处理线程 {worker_id}: 收到停止信号")
                    break

                frame_id, outputs, inference_time, npu_worker_id = raw_data
                start_time = time.time()

                if outputs is not None:
                    # 后处理 - 完全按照detect_v2.py的方式
                    input0_data = outputs[0].reshape([3, -1] + list(outputs[0].shape[-2:]))
                    input1_data = outputs[1].reshape([3, -1] + list(outputs[1].shape[-2:]))
                    input2_data = outputs[2].reshape([3, -1] + list(outputs[2].shape[-2:]))
                    input_data = [np.transpose(input0_data, (2, 3, 0, 1)),
                                  np.transpose(input1_data, (2, 3, 0, 1)),
                                  np.transpose(input2_data, (2, 3, 0, 1))]

                    # 使用优化的后处理函数
                    boxes, classes, scores = yolov5_post_process_optimized(input_data)
                else:
                    # RKNN不可用，返回空结果
                    boxes, classes, scores = None, None, None

                postprocess_time = time.time() - start_time
                total_time = inference_time + postprocess_time

                self.final_result_queue.put((frame_id, boxes, classes, scores, inference_time,
                                          postprocess_time, total_time, npu_worker_id, worker_id))
                self.raw_result_queue.task_done()

            except Empty:
                continue
            except Exception as e:
                logger.error(f"后处理线程 {worker_id} 出错: {e}")
                break

        logger.info(f"后处理线程 {worker_id} 退出")



    def detect_frame(self, frame) -> Optional[List[Dict]]:
        """
        检测单帧图像（兼容现有接口）

        Args:
            frame: OpenCV图像帧

        Returns:
            检测结果列表
        """
        try:
            # 外部状态检查 - 防止绕过GUI状态控制
            if hasattr(self, 'external_state_checker') and self.external_state_checker:
                if not self.external_state_checker():
                    return []  # 外部状态不允许检测，返回空结果

            if not self.is_loaded:
                logger.error("模型未加载")
                return None

            if frame is None:
                logger.error("输入帧为空")
                return None

            # 如果异步处理可用，提交到队列
            if RKNN_AVAILABLE and self.img_queue is not None:
                # 预处理
                img, _, _ = letterbox(frame, new_shape=(IMG_SIZE, IMG_SIZE))
                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                img = np.expand_dims(img, 0)

                # 非阻塞提交 - 完全按照detect_v2.py
                frame_id = int(time.time() * 1000000)  # 使用微秒时间戳
                try:
                    self.img_queue.put_nowait((frame_id, img))
                    self.frame_stats['submitted'] += 1
                    self.frame_count += 1
                except:
                    self.frame_stats['dropped'] += 1

                # 每60帧打印一次统计（与detect_v2.py一致）
                if self.frame_count % 60 == 0:
                    logger.info(f"帧统计: 提交: {self.frame_stats['submitted']}, "
                              f"处理: {self.frame_stats['processed']}, 检测: {self.frame_stats['detected']}, 丢弃: {self.frame_stats['dropped']}")

                # 获取结果（非阻塞） - 优化：只获取最新结果，减少延迟
                latest_result = None
                result_count = 0
                try:
                    while True:
                        result_data = self.final_result_queue.get_nowait()
                        _, boxes, classes, scores, inference_time, postprocess_time, total_time, _, _ = result_data
                        latest_result = (boxes, classes, scores)
                        result_count += 1
                        self.frame_stats['processed'] += 1
                        if boxes is not None:
                            self.frame_stats['detected'] += 1
                            # 多目标时打印详细信息（与detect_v2.py一致）
                            if len(boxes) > 5:
                                logger.info(f"多目标检测: {len(boxes)}个目标, 推理:{inference_time:.3f}s, 后处理:{postprocess_time:.3f}s, 总计:{total_time:.3f}s")
                except Empty:
                    pass

                # 如果处理了多个结果，记录延迟情况
                if result_count > 1:
                    logger.debug(f"处理了{result_count}个积压结果，使用最新结果")

                # 处理结果
                if latest_result is not None:
                    boxes, classes, scores = latest_result
                    if boxes is not None:
                        formatted_results = self._format_detection_results(boxes, classes, scores)
                        # 更新last_detection和时间戳
                        if formatted_results:
                            self.last_detection = formatted_results[0]  # 保存最佳检测结果
                            self.last_detection['timestamp'] = time.time()  # 添加时间戳
                        return formatted_results

                # 没有新结果时，返回上次检测结果（如果时间不超过1秒，减少延迟）
                if hasattr(self, 'last_detection') and self.last_detection is not None:
                    if 'timestamp' in self.last_detection:
                        time_diff = time.time() - self.last_detection['timestamp']
                        if time_diff < 1.0:  # 1秒内的结果仍然有效，提高实时性
                            return [self.last_detection]

                return []
            else:
                # RKNN不可用，返回空结果
                logger.warning("RKNN不可用，无法进行检测")
                return []

        except Exception as e:
            logger.error(f"帧检测失败: {e}")
            return None

    def _format_detection_results(self, boxes, classes, scores) -> List[Dict]:
        """格式化检测结果为标准格式"""
        if boxes is None or len(boxes) == 0:
            self.last_detection = None
            return []
        
        detections = []
        for box, cls, score in zip(boxes, classes, scores):
            if self._is_in_roi(box):
                detection = {
                    'bbox': [int(x) for x in box],
                    'confidence': float(score),
                    'class_id': int(cls),
                    'class_name': CLASSES[int(cls)]
                }
                detections.append(detection)
        
        if detections:
            # 选择置信度最高的检测结果作为last_detection
            best_detection = max(detections, key=lambda x: x['confidence'])
            class_id = best_detection['class_id']
            
            if class_id in self.class_mapping:
                garbage_info = self.class_mapping[class_id]
                
                self.last_detection = {
                    'class_id': class_id,
                    'class_name': best_detection['class_name'],  # 使用原始检测结果的class_name
                    'display_name': garbage_info['display'],
                    'garbage_id': garbage_info['id'],
                    'confidence': best_detection['confidence'],
                    'bbox': best_detection['bbox'],
                    'timestamp': datetime.now()
                }
                
                logger.info(f"RKNN检测结果: {garbage_info['display']}, 置信度: {best_detection['confidence']:.2f}")
            
            return detections
        else:
            self.last_detection = None
            return []

    def _is_in_roi(self, bbox: List[int], purpose: str = 'detection') -> bool:
        """检查边界框是否在ROI区域内"""
        x1, y1, x2, y2 = bbox
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2

        for roi in self.roi_areas:
            if roi.get('purpose') == purpose:
                roi_x1, roi_y1, roi_x2, roi_y2 = roi['area']
                if roi_x1 <= center_x <= roi_x2 and roi_y1 <= center_y <= roi_y2:
                    return True

        return False

    def get_detection_in_area(self, bbox: List[int]) -> Optional[str]:
        """获取检测框所在的区域名称"""
        x1, y1, x2, y2 = bbox
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2

        for roi in self.roi_areas:
            roi_x1, roi_y1, roi_x2, roi_y2 = roi['area']
            if roi_x1 <= center_x <= roi_x2 and roi_y1 <= center_y <= roi_y2:
                return roi['name']

        return None

    # 兼容现有接口的其他方法
    def get_last_detection(self) -> Optional[Dict[str, Any]]:
        """获取最后一次检测结果"""
        return self.last_detection
    
    def get_detection_stats(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        return self.detection_stats.copy()
    
    def clear_detection_results(self):
        """清除检测结果 - 用于分拣完成后清除显示的检测框"""
        self.last_detection = None
        self.detection_confidence = 0.0
        logger.info("检测结果已清除")

    def reset_stats(self):
        """重置检测统计"""
        self.detection_stats = {
            'total_detections': 0,
            'successful_detections': 0,
            'failed_detections': 0,
            'average_confidence': 0.0
        }
        self.frame_stats = {
            'submitted': 0,
            'processed': 0,
            'detected': 0,
            'dropped': 0
        }
        logger.info("检测统计信息已重置")

    def get_performance_stats(self):
        """获取性能统计信息"""
        stats = self.frame_stats.copy()
        if stats['submitted'] > 0:
            stats['detection_rate'] = stats['detected'] / stats['submitted']
            stats['drop_rate'] = stats['dropped'] / stats['submitted']
            stats['process_rate'] = stats['processed'] / stats['submitted']
        else:
            stats['detection_rate'] = 0.0
            stats['drop_rate'] = 0.0
            stats['process_rate'] = 0.0
        return stats
    
    def is_model_loaded(self) -> bool:
        """检查模型是否已加载"""
        return self.is_loaded
    
    def is_detection_active(self) -> bool:
        """检查检测是否激活"""
        return self.is_detecting
    
    def get_class_mapping(self) -> Dict[int, Dict[str, Any]]:
        """获取类别映射"""
        return self.class_mapping.copy()
    
    def set_confidence_threshold(self, threshold: float):
        """设置置信度阈值"""
        if 0.0 <= threshold <= 1.0:
            self.conf_thres = threshold
            global OBJ_THRESH
            OBJ_THRESH = threshold
            logger.info(f"设置置信度阈值: {threshold}")
        else:
            logger.error(f"无效的置信度阈值: {threshold}")

    def get_roi_areas(self) -> List[Dict]:
        """获取ROI区域配置"""
        return self.roi_areas.copy()

    def __del__(self):
        """析构函数"""
        try:
            self.stop_detection()
            if self.multi_npu_detector:
                self.multi_npu_detector.release_all()
        except:
            pass