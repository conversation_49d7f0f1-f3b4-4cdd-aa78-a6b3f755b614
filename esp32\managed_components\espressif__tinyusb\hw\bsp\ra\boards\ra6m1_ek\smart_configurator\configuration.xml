<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<raConfiguration version="9">
  <generalSettings>
    <option key="#Board#" value="board.ra6m1ek"/>
    <option key="CPU" value="RA6M1"/>
    <option key="Core" value="CM4"/>
    <option key="#TargetName#" value="R7FA6M1AD3CFP"/>
    <option key="#TargetARCHITECTURE#" value="cortex-m4"/>
    <option key="#DeviceCommand#" value="R7FA6M1AD"/>
    <option key="#RTOS#" value="_none"/>
    <option key="#pinconfiguration#" value="R7FA6M1AD3CFP.pincfg"/>
    <option key="#FSPVersion#" value="5.6.0"/>
    <option key="#ConfigurationFragments#" value="Renesas##BSP##Board##ra6m1_ek##"/>
    <option key="#SELECTED_TOOLCHAIN#" value="com.renesas.cdt.managedbuild.gnuarm.toolchain."/>
  </generalSettings>
  <raBspConfiguration>
    <config id="config.bsp.ra6m1.R7FA6M1AD3CFP">
      <property id="config.bsp.part_number" value="config.bsp.part_number.value"/>
      <property id="config.bsp.rom_size_bytes" value="config.bsp.rom_size_bytes.value"/>
      <property id="config.bsp.rom_size_bytes_hidden" value="524288"/>
      <property id="config.bsp.ram_size_bytes" value="config.bsp.ram_size_bytes.value"/>
      <property id="config.bsp.data_flash_size_bytes" value="config.bsp.data_flash_size_bytes.value"/>
      <property id="config.bsp.package_style" value="config.bsp.package_style.value"/>
      <property id="config.bsp.package_pins" value="config.bsp.package_pins.value"/>
      <property id="config.bsp.irq_count_hidden" value="96"/>
    </config>
    <config id="config.bsp.ra6m1">
      <property id="config.bsp.series" value="config.bsp.series.value"/>
    </config>
    <config id="config.bsp.ra6m1.fsp">
      <property id="config.bsp.fsp.inline_irq_functions" value="config.bsp.common.inline_irq_functions.enabled"/>
      <property id="config.bsp.fsp.OFS0.iwdt_start_mode" value="config.bsp.fsp.OFS0.iwdt_start_mode.disabled"/>
      <property id="config.bsp.fsp.OFS0.iwdt_timeout" value="config.bsp.fsp.OFS0.iwdt_timeout.2048"/>
      <property id="config.bsp.fsp.OFS0.iwdt_divisor" value="config.bsp.fsp.OFS0.iwdt_divisor.128"/>
      <property id="config.bsp.fsp.OFS0.iwdt_window_end" value="config.bsp.fsp.OFS0.iwdt_window_end.0"/>
      <property id="config.bsp.fsp.OFS0.iwdt_window_start" value="config.bsp.fsp.OFS0.iwdt_window_start.100"/>
      <property id="config.bsp.fsp.OFS0.iwdt_reset_interrupt" value="config.bsp.fsp.OFS0.iwdt_reset_interrupt.Reset"/>
      <property id="config.bsp.fsp.OFS0.iwdt_stop_control" value="config.bsp.fsp.OFS0.iwdt_stop_control.stops"/>
      <property id="config.bsp.fsp.OFS0.wdt_start_mode" value="config.bsp.fsp.OFS0.wdt_start_mode.register"/>
      <property id="config.bsp.fsp.OFS0.wdt_timeout" value="config.bsp.fsp.OFS0.wdt_timeout.16384"/>
      <property id="config.bsp.fsp.OFS0.wdt_divisor" value="config.bsp.fsp.OFS0.wdt_divisor.128"/>
      <property id="config.bsp.fsp.OFS0.wdt_window_end" value="config.bsp.fsp.OFS0.wdt_window_end.0"/>
      <property id="config.bsp.fsp.OFS0.wdt_window_start" value="config.bsp.fsp.OFS0.wdt_window_start.100"/>
      <property id="config.bsp.fsp.OFS0.wdt_reset_interrupt" value="config.bsp.fsp.OFS0.wdt_reset_interrupt.Reset"/>
      <property id="config.bsp.fsp.OFS0.wdt_stop_control" value="config.bsp.fsp.OFS0.wdt_stop_control.stops"/>
      <property id="config.bsp.fsp.OFS1.voltage_detection0.start" value="config.bsp.fsp.OFS1.voltage_detection0.start.disabled"/>
      <property id="config.bsp.fsp.OFS1.voltage_detection0_level" value="config.bsp.fsp.OFS1.voltage_detection0_level.280"/>
      <property id="config.bsp.fsp.OFS1.hoco_osc" value="config.bsp.fsp.OFS1.hoco_osc.disabled"/>
      <property id="config.bsp.fsp.mpu_pc0_enable" value="config.bsp.fsp.mpu_pc0_enable.disabled"/>
      <property id="config.bsp.fsp.mpu_pc0_start" value="0xFFFFFFFC"/>
      <property id="config.bsp.fsp.mpu_pc0_end" value="0xFFFFFFFF"/>
      <property id="config.bsp.fsp.mpu_pc1_enable" value="config.bsp.fsp.mpu_pc1_enable.disabled"/>
      <property id="config.bsp.fsp.mpu_pc1_start" value="0xFFFFFFFC"/>
      <property id="config.bsp.fsp.mpu_pc1_end" value="0xFFFFFFFF"/>
      <property id="config.bsp.fsp.mpu_reg0_enable" value="config.bsp.fsp.mpu_reg0_enable.disabled"/>
      <property id="config.bsp.fsp.mpu_reg0_start" value="0x00FFFFFC"/>
      <property id="config.bsp.fsp.mpu_reg0_end" value="0x00FFFFFF"/>
      <property id="config.bsp.fsp.mpu_reg1_enable" value="config.bsp.fsp.mpu_reg1_enable.disabled"/>
      <property id="config.bsp.fsp.mpu_reg1_start" value="0x200FFFFC"/>
      <property id="config.bsp.fsp.mpu_reg1_end" value="0x200FFFFF"/>
      <property id="config.bsp.fsp.mpu_reg2_enable" value="config.bsp.fsp.mpu_reg2_enable.disabled"/>
      <property id="config.bsp.fsp.mpu_reg2_start" value="0x407FFFFC"/>
      <property id="config.bsp.fsp.mpu_reg2_end" value="0x407FFFFF"/>
      <property id="config.bsp.fsp.mpu_reg3_enable" value="config.bsp.fsp.mpu_reg3_enable.disabled"/>
      <property id="config.bsp.fsp.mpu_reg3_start" value="0x400DFFFC"/>
      <property id="config.bsp.fsp.mpu_reg3_end" value="0x400DFFFF"/>
      <property id="config.bsp.fsp.hoco_fll" value="config.bsp.fsp.hoco_fll.disabled"/>
      <property id="config.bsp.common.main_osc_wait" value="config.bsp.common.main_osc_wait.wait_8163"/>
      <property id="config.bsp.fsp.mcu.adc.max_freq_hz" value="60000000"/>
      <property id="config.bsp.fsp.mcu.sci_uart.max_baud" value="20000000"/>
      <property id="config.bsp.fsp.mcu.adc.sample_and_hold" value="1"/>
      <property id="config.bsp.fsp.mcu.adc.sensors_are_exclusive" value="0"/>
      <property id="config.bsp.fsp.mcu.sci_spi.max_bitrate" value="30000000"/>
      <property id="config.bsp.fsp.mcu.spi.max_bitrate" value="30000000"/>
      <property id="config.bsp.fsp.mcu.iic_master.rate.rate_fastplus" value="1"/>
      <property id="config.bsp.fsp.mcu.iic_master.fastplus_channels" value="0x1"/>
      <property id="config.bsp.fsp.mcu.iic_slave.rate.rate_fastplus" value="1"/>
      <property id="config.bsp.fsp.mcu.iic_slave.fastplus_channels" value="0x1"/>
      <property id="config.bsp.fsp.mcu.sci_uart.cstpen_channels" value="0x0"/>
      <property id="config.bsp.fsp.mcu.gpt.pin_count_source_channels" value="0xFFFF"/>
      <property id="config.bsp.common.id_mode" value="config.bsp.common.id_mode.unlocked"/>
      <property id="config.bsp.common.id_code" value="FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF"/>
      <property id="config.bsp.common.id1" value=""/>
      <property id="config.bsp.common.id2" value=""/>
      <property id="config.bsp.common.id3" value=""/>
      <property id="config.bsp.common.id4" value=""/>
      <property id="config.bsp.common.id_fixed" value=""/>
    </config>
    <config id="config.bsp.ra">
      <property id="config.bsp.common.main" value="0x1000"/>
      <property id="config.bsp.common.heap" value="0x1000"/>
      <property id="config.bsp.common.vcc" value="3300"/>
      <property id="config.bsp.common.checking" value="config.bsp.common.checking.disabled"/>
      <property id="config.bsp.common.assert" value="config.bsp.common.assert.none"/>
      <property id="config.bsp.common.error_log" value="config.bsp.common.error_log.none"/>
      <property id="config.bsp.common.soft_reset" value="config.bsp.common.soft_reset.disabled"/>
      <property id="config.bsp.common.main_osc_populated" value="config.bsp.common.main_osc_populated.enabled"/>
      <property id="config.bsp.common.pfs_protect" value="config.bsp.common.pfs_protect.enabled"/>
      <property id="config.bsp.common.c_runtime_init" value="config.bsp.common.c_runtime_init.enabled"/>
      <property id="config.bsp.common.early_init" value="config.bsp.common.early_init.disabled"/>
      <property id="config.bsp.common.main_osc_clock_source" value="config.bsp.common.main_osc_clock_source.crystal"/>
      <property id="config.bsp.common.subclock_populated" value="config.bsp.common.subclock_populated.enabled"/>
      <property id="config.bsp.common.subclock_drive" value="config.bsp.common.subclock_drive.standard"/>
      <property id="config.bsp.common.subclock_stabilization_ms" value="1000"/>
    </config>
  </raBspConfiguration>
  <raClockConfiguration>
    <node id="board.clock.xtal.freq" mul="12000000" option="_edit"/>
    <node id="board.clock.pll.source" option="board.clock.pll.source.xtal"/>
    <node id="board.clock.hoco.freq" option="board.clock.hoco.freq.20m"/>
    <node id="board.clock.loco.freq" option="board.clock.loco.freq.32768"/>
    <node id="board.clock.moco.freq" option="board.clock.moco.freq.8m"/>
    <node id="board.clock.subclk.freq" option="board.clock.subclk.freq.32768"/>
    <node id="board.clock.pll.div" option="board.clock.pll.div.1"/>
    <node id="board.clock.pll.mul" option="board.clock.pll.mul.200"/>
    <node id="board.clock.pll.display" option="board.clock.pll.display.value"/>
    <node id="board.clock.clock.source" option="board.clock.clock.source.pll"/>
    <node id="board.clock.iclk.div" option="board.clock.iclk.div.2"/>
    <node id="board.clock.iclk.display" option="board.clock.iclk.display.value"/>
    <node id="board.clock.pclka.div" option="board.clock.pclka.div.2"/>
    <node id="board.clock.pclka.display" option="board.clock.pclka.display.value"/>
    <node id="board.clock.pclkb.div" option="board.clock.pclkb.div.4"/>
    <node id="board.clock.pclkb.display" option="board.clock.pclkb.display.value"/>
    <node id="board.clock.pclkc.div" option="board.clock.pclkc.div.4"/>
    <node id="board.clock.pclkc.display" option="board.clock.pclkc.display.value"/>
    <node id="board.clock.pclkd.div" option="board.clock.pclkd.div.2"/>
    <node id="board.clock.pclkd.display" option="board.clock.pclkd.display.value"/>
    <node id="board.clock.bclk.div" option="board.clock.bclk.div.2"/>
    <node id="board.clock.bclk.display" option="board.clock.bclk.display.value"/>
    <node id="board.clock.bclkout.div" option="board.clock.bclkout.div.2"/>
    <node id="board.clock.bclkout.display" option="board.clock.bclkout.display.value"/>
    <node id="board.clock.uclk.div" option="board.clock.uclk.div.5"/>
    <node id="board.clock.uclk.display" option="board.clock.uclk.display.value"/>
    <node id="board.clock.fclk.div" option="board.clock.fclk.div.4"/>
    <node id="board.clock.fclk.display" option="board.clock.fclk.display.value"/>
    <node id="board.clock.clkout.source" option="board.clock.clkout.source.disabled"/>
    <node id="board.clock.clkout.div" option="board.clock.clkout.div.1"/>
    <node id="board.clock.clkout.display" option="board.clock.clkout.display.value"/>
  </raClockConfiguration>
  <raComponentSelection>
    <component apiversion="" class="Projects" condition="" group="all" subgroup="baremetal_blinky" variant="" vendor="Renesas" version="5.6.0">
      <description>Simple application that blinks an LED. No RTOS included.</description>
      <originalPack>Renesas.RA_baremetal_blinky.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="Common" condition="" group="all" subgroup="fsp_common" variant="" vendor="Renesas" version="5.6.0">
      <description>Board Support Package Common Files</description>
      <originalPack>Renesas.RA.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="HAL Drivers" condition="" group="all" subgroup="r_ioport" variant="" vendor="Renesas" version="5.6.0">
      <description>I/O Port</description>
      <originalPack>Renesas.RA.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="CMSIS" condition="" group="CMSIS5" subgroup="CoreM" variant="" vendor="Arm" version="6.1.0+fsp.5.6.0">
      <description>Arm CMSIS Version 6 - Core (M)</description>
      <originalPack>Arm.CMSIS6.6.1.0+fsp.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="Board" subgroup="ra6m1_ek" variant="" vendor="Renesas" version="5.6.0">
      <description>RA6M1-EK Board Support Files</description>
      <originalPack>Renesas.RA_board_ra6m1_ek.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="ra6m1" subgroup="device" variant="R7FA6M1AD3CFP" vendor="Renesas" version="5.6.0">
      <description>Board support package for R7FA6M1AD3CFP</description>
      <originalPack>Renesas.RA_mcu_ra6m1.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="ra6m1" subgroup="device" variant="" vendor="Renesas" version="5.6.0">
      <description>Board support package for RA6M1</description>
      <originalPack>Renesas.RA_mcu_ra6m1.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="ra6m1" subgroup="fsp" variant="" vendor="Renesas" version="5.6.0">
      <description>Board support package for RA6M1 - FSP Data</description>
      <originalPack>Renesas.RA_mcu_ra6m1.5.6.0.pack</originalPack>
    </component>
    <component apiversion="" class="BSP" condition="" group="ra6m1" subgroup="events" variant="" vendor="Renesas" version="5.6.0">
      <description>Board support package for RA6M1 - Events</description>
      <originalPack>Renesas.RA_mcu_ra6m1.5.6.0.pack</originalPack>
    </component>
  </raComponentSelection>
  <raElcConfiguration/>
  <raIcuConfiguration/>
  <raModuleConfiguration>
    <module id="module.driver.ioport_on_ioport.0">
      <property id="module.driver.ioport.name" value="g_ioport"/>
      <property id="module.driver.ioport.elc_trigger_ioport1" value="_disabled"/>
      <property id="module.driver.ioport.elc_trigger_ioport2" value="_disabled"/>
      <property id="module.driver.ioport.elc_trigger_ioport3" value="_disabled"/>
      <property id="module.driver.ioport.elc_trigger_ioport4" value="_disabled"/>
      <property id="module.driver.ioport.pincfg" value="g_bsp_pin_cfg"/>
    </module>
    <context id="_hal.0">
      <stack module="module.driver.ioport_on_ioport.0"/>
    </context>
    <config id="config.driver.ioport">
      <property id="config.driver.ioport.checking" value="config.driver.ioport.checking.system"/>
    </config>
  </raModuleConfiguration>
  <raPinConfiguration>
    <symbolicName propertyId="p112.symbolic_name" value="LED1"/>
    <symbolicName propertyId="p415.symbolic_name" value="SW1"/>
    <pincfg active="true" name="RA6M1-EK.pincfg" selected="true" symbol="g_bsp_pin_cfg">
      <configSetting altId="adc1.an00.p004" configurationId="adc1.an00"/>
      <configSetting altId="adc1.mode.custom" configurationId="adc1.mode"/>
      <configSetting altId="ctsu0.mode.enabled" configurationId="ctsu0.mode"/>
      <configSetting altId="ctsu0.ts02.p207" configurationId="ctsu0.ts02"/>
      <configSetting altId="ctsu0.tscap.p205" configurationId="ctsu0.tscap"/>
      <configSetting altId="debug0.mode.swd" configurationId="debug0.mode"/>
      <configSetting altId="debug0.swclk.p300" configurationId="debug0.swclk"/>
      <configSetting altId="debug0.swdio.p108" configurationId="debug0.swdio"/>
      <configSetting altId="p004.asel" configurationId="p004"/>
      <configSetting altId="p004.gpio_mode.gpio_mode_an" configurationId="p004.gpio_mode"/>
      <configSetting altId="p100.spi0.miso" configurationId="p100"/>
      <configSetting altId="p100.gpio_mode.gpio_mode_peripheral" configurationId="p100.gpio_mode"/>
      <configSetting altId="p101.spi0.mosi" configurationId="p101"/>
      <configSetting altId="p101.gpio_mode.gpio_mode_peripheral" configurationId="p101.gpio_mode"/>
      <configSetting altId="p102.spi0.rspck" configurationId="p102"/>
      <configSetting altId="p102.gpio_mode.gpio_mode_peripheral" configurationId="p102.gpio_mode"/>
      <configSetting altId="p103.spi0.ssl0" configurationId="p103"/>
      <configSetting altId="p103.gpio_mode.gpio_mode_peripheral" configurationId="p103.gpio_mode"/>
      <configSetting altId="p104.sci8.rxd" configurationId="p104"/>
      <configSetting altId="p104.gpio_mode.gpio_mode_peripheral" configurationId="p104.gpio_mode"/>
      <configSetting altId="p105.sci8.txd" configurationId="p105"/>
      <configSetting altId="p105.gpio_mode.gpio_mode_peripheral" configurationId="p105.gpio_mode"/>
      <configSetting altId="p106.output.low" configurationId="p106"/>
      <configSetting altId="p106.gpio_mode.gpio_mode_out.low" configurationId="p106.gpio_mode"/>
      <configSetting altId="p107.output.low" configurationId="p107"/>
      <configSetting altId="p107.gpio_mode.gpio_mode_out.low" configurationId="p107.gpio_mode"/>
      <configSetting altId="p108.debug0.swdio" configurationId="p108"/>
      <configSetting altId="p108.gpio_mode.gpio_mode_peripheral" configurationId="p108.gpio_mode"/>
      <configSetting altId="p112.output.low" configurationId="p112"/>
      <configSetting altId="p112.gpio_mode.gpio_mode_out.low" configurationId="p112.gpio_mode"/>
      <configSetting altId="p201.input" configurationId="p201"/>
      <configSetting altId="p201.gpio_mode.gpio_mode_in" configurationId="p201.gpio_mode"/>
      <configSetting altId="p205.ctsu0.tscap" configurationId="p205"/>
      <configSetting altId="p205.gpio_mode.gpio_mode_peripheral" configurationId="p205.gpio_mode"/>
      <configSetting altId="p207.ctsu0.ts02" configurationId="p207"/>
      <configSetting altId="p207.gpio_mode.gpio_mode_peripheral" configurationId="p207.gpio_mode"/>
      <configSetting altId="p300.debug0.swclk" configurationId="p300"/>
      <configSetting altId="p300.gpio_mode.gpio_mode_peripheral" configurationId="p300.gpio_mode"/>
      <configSetting altId="p407.usbfs0.vbus" configurationId="p407"/>
      <configSetting altId="p407.gpio_mode.gpio_mode_peripheral" configurationId="p407.gpio_mode"/>
      <configSetting altId="p415.input" configurationId="p415"/>
      <configSetting altId="p415.gpio_mode.gpio_mode_in" configurationId="p415.gpio_mode"/>
      <configSetting altId="p415.gpio_pupd.gpio_pupd_ip_up" configurationId="p415.gpio_pupd"/>
      <configSetting altId="sci8.mode.asynchronous.free" configurationId="sci8.mode"/>
      <configSetting altId="sci8.rxd.p104" configurationId="sci8.rxd"/>
      <configSetting altId="sci8.txd.p105" configurationId="sci8.txd"/>
      <configSetting altId="spi0.miso.p100" configurationId="spi0.miso"/>
      <configSetting altId="spi0.mode.enabled.a" configurationId="spi0.mode"/>
      <configSetting altId="spi0.mosi.p101" configurationId="spi0.mosi"/>
      <configSetting altId="spi0.rspck.p102" configurationId="spi0.rspck"/>
      <configSetting altId="spi0.ssl0.p103" configurationId="spi0.ssl0"/>
      <configSetting altId="usbfs0.mode.device" configurationId="usbfs0.mode"/>
      <configSetting altId="usbfs0.vbus.p407" configurationId="usbfs0.vbus"/>
    </pincfg>
    <pincfg active="false" name="R7FA6M1AD3CFP.pincfg" selected="false" symbol="">
      <configSetting altId="debug0.mode.jtag" configurationId="debug0.mode"/>
      <configSetting altId="debug0.tck.p300" configurationId="debug0.tck"/>
      <configSetting altId="debug0.tdi.p110" configurationId="debug0.tdi"/>
      <configSetting altId="debug0.tdo.p109" configurationId="debug0.tdo"/>
      <configSetting altId="debug0.tms.p108" configurationId="debug0.tms"/>
      <configSetting altId="p108.debug0.tms" configurationId="p108"/>
      <configSetting altId="p108.gpio_mode.gpio_mode_peripheral" configurationId="p108.gpio_mode"/>
      <configSetting altId="p109.debug0.tdo" configurationId="p109"/>
      <configSetting altId="p109.gpio_mode.gpio_mode_peripheral" configurationId="p109.gpio_mode"/>
      <configSetting altId="p110.debug0.tdi" configurationId="p110"/>
      <configSetting altId="p110.gpio_mode.gpio_mode_peripheral" configurationId="p110.gpio_mode"/>
      <configSetting altId="p300.debug0.tck" configurationId="p300"/>
      <configSetting altId="p300.gpio_mode.gpio_mode_peripheral" configurationId="p300.gpio_mode"/>
    </pincfg>
  </raPinConfiguration>
</raConfiguration>
