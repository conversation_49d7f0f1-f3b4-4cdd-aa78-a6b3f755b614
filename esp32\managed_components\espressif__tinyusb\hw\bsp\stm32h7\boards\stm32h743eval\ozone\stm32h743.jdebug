
/*********************************************************************
*
*       OnProjectLoad
*
* Function description
*   Project load routine. Required.
*
**********************************************************************
*/
void OnProjectLoad (void) {
  Project.AddSvdFile ("$(InstallDir)/Config/CPU/Cortex-M7F.svd");
  Project.AddSvdFile ("$(InstallDir)/Config/Peripherals/ARMv7M.svd");
  Project.AddSvdFile ("./STM32H743.svd");

  Project.SetDevice ("STM32H743XI");
  Project.SetHostIF ("USB", "");
  Project.SetTargetIF ("SWD");
  Project.SetTIFSpeed ("50 MHz");

  Project.SetTraceSource ("Trace Pins");
  Project.SetTracePortWidth (4);
  // timing delay for trace pins in pico seconds, default is 2 nano seconds
  Project.SetTraceTiming (100, 100, 100, 100);

  File.Open ("../../../../../../examples/device/cdc_msc/cmake-build-stm32h743eval/cdc_msc.elf");
}

/*********************************************************************
*0
*      TargetReset
*
* Function description
*   Replaces the default target device reset routine. Optional.
*
* Notes
*   This example demonstrates the usage when
*   debugging a RAM program on a Cortex-M target device
*
**********************************************************************
*/
//void TargetReset (void) {
//
//  unsigned int SP;
//  unsigned int PC;
//  unsigned int VectorTableAddr;
//
//  Exec.Reset();
//
//  VectorTableAddr = Elf.GetBaseAddr();
//
//  if (VectorTableAddr != 0xFFFFFFFF) {
//
//    Util.Log("Resetting Program.");
//
//    SP = Target.ReadU32(VectorTableAddr);
//    Target.SetReg("SP", SP);
//
//    PC = Target.ReadU32(VectorTableAddr + 4);
//    Target.SetReg("PC", PC);
//  }
//}

/*********************************************************************
*
*       BeforeTargetReset
*
* Function description
*   Event handler routine. Optional.
*
**********************************************************************
*/
//void BeforeTargetReset (void) {
//}

/*********************************************************************
*
*       AfterTargetReset
*
* Function description
*   Event handler routine.
*    - Sets the PC register to program reset value.
*    - Sets the SP register to program reset value on Cortex-M.
*
**********************************************************************
*/
void AfterTargetReset (void) {
  unsigned int SP;
  unsigned int PC;
  unsigned int VectorTableAddr;

  VectorTableAddr = Elf.GetBaseAddr();

  if (VectorTableAddr == 0xFFFFFFFF) {
    Util.Log("Project file error: failed to get program base");
  } else {
    SP = Target.ReadU32(VectorTableAddr);
    Target.SetReg("SP", SP);

    PC = Target.ReadU32(VectorTableAddr + 4);
    Target.SetReg("PC", PC);
  }
}

/*********************************************************************
*
*       DebugStart
*
* Function description
*   Replaces the default debug session startup routine. Optional.
*
**********************************************************************
*/
//void DebugStart (void) {
//}

/*********************************************************************
*
*       TargetConnect
*
* Function description
*   Replaces the default target IF connection routine. Optional.
*
**********************************************************************
*/
//void TargetConnect (void) {
//}

/*********************************************************************
*
*       BeforeTargetConnect
*
* Function description
*   Event handler routine. Optional.
*
**********************************************************************
*/

void BeforeTargetConnect (void) {
  //
  // Trace pin init is done by J-Link script file as J-Link script files are IDE independent
  //
  //Project.SetJLinkScript("./ST_STM32H743_Traceconfig.pex");
}

/*********************************************************************
*
*       AfterTargetConnect
*
* Function description
*   Event handler routine. Optional.
*
**********************************************************************
*/
//void AfterTargetConnect (void) {
//}

/*********************************************************************
*
*       TargetDownload
*
* Function description
*   Replaces the default program download routine. Optional.
*
**********************************************************************
*/
//void TargetDownload (void) {
//}

/*********************************************************************
*
*       BeforeTargetDownload
*
* Function description
*   Event handler routine. Optional.
*
**********************************************************************
*/
//void BeforeTargetDownload (void) {
//}

/*********************************************************************
*
*      AfterTargetDownload
*
* Function description
*   Event handler routine.
*    - Sets the PC register to program reset value.
*    - Sets the SP register to program reset value on Cortex-M.
*
**********************************************************************
*/
void AfterTargetDownload (void) {
  unsigned int SP;
  unsigned int PC;
  unsigned int VectorTableAddr;

  VectorTableAddr = Elf.GetBaseAddr();
  Util.Log("___");
  if (VectorTableAddr == 0xFFFFFFFF) {
    Util.Log("Project file error: failed to get program base");
  } else {
    SP = Target.ReadU32(VectorTableAddr);
    Target.SetReg("SP", SP);

    PC = Target.ReadU32(VectorTableAddr + 4);
    Target.SetReg("PC", PC);
  }
}

/*********************************************************************
*
*       BeforeTargetDisconnect
*
* Function description
*   Event handler routine. Optional.
*
**********************************************************************
*/
//void BeforeTargetDisconnect (void) {
//}

/*********************************************************************
*
*       AfterTargetDisconnect
*
* Function description
*   Event handler routine. Optional.
*
**********************************************************************
*/
//void AfterTargetDisconnect (void) {
//}

/*********************************************************************
*
*       AfterTargetHalt
*
* Function description
*   Event handler routine. Optional.
*
**********************************************************************
*/
//void AfterTargetHalt (void) {
//}
